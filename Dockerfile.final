ARG BASE_IMG=rtl2gds:base
ARG IEDA_IMG=docker.cnb.cool/ecoslab/rtl2gds/ieda:latest

FROM $IEDA_IMG AS ieda
FROM $BASE_IMG AS base

# build magic and yosys
FROM base AS build

ARG MAGIC_VERSION=8.3.530
ARG YOSYS_VERSION=0.54
ARG NPROC=64

RUN apt-get update && apt-get install -y --no-install-recommends wget unzip git\
    m4 libx11-dev tcl-dev tk-dev libcairo2-dev libncurses-dev zlib1g-dev libreadline-dev \
    build-essential clang lld bison flex gawk tcl-dev libffi-dev git graphviz \
    xdot pkg-config python3 libboost-system-dev libboost-python-dev libboost-filesystem-dev

RUN wget --no-check-certificate https://githubfast.com/RTimothyEdwards/magic/archive/refs/tags/${MAGIC_VERSION}.zip -O /tmp/magic.zip && \
    unzip -q /tmp/magic.zip -d /tmp && cd /tmp/magic-${MAGIC_VERSION} && ./configure && make -j ${NPROC} && make install -j ${NPROC} && \
    if [ ! -f /usr/local/lib/magic/sys/minimum.tech ]; then \
      echo "ERROR: /usr/local/lib/magic/sys/minimum.tech not found after magic install!" >&2; \
      exit 1; \
    fi

RUN wget --no-check-certificate https://githubfast.com/YosysHQ/yosys/releases/download/v${YOSYS_VERSION}/yosys.tar.gz -O /tmp/yosys.tar.gz && \
    mkdir /tmp/yosys && tar -xzf /tmp/yosys.tar.gz -C /tmp/yosys && cd /tmp/yosys && make -j ${NPROC} && make install -j ${NPROC}

ADD ./bin/yosys/share/yosys/plugins/slang.so /usr/local/share/yosys/plugins/slang.so

FROM base

COPY --from=build /usr/local/bin/magic /usr/local/bin/magic
COPY --from=build /usr/local/lib/magic /usr/local/lib/magic

COPY --from=build /usr/local/bin/yosys* /usr/local/bin/
COPY --from=build /usr/local/share/yosys /usr/local/share/yosys

# build ieda (copy binary)
COPY --from=ieda /opt/iEDA/bin/iEDA /usr/local/bin/iEDA

ARG R2G_WORKSPACE=/opt/rtl2gds
WORKDIR ${R2G_WORKSPACE}

CMD ["/usr/bin/env", "python3", "-m", "rtl2gds", "-h"]
