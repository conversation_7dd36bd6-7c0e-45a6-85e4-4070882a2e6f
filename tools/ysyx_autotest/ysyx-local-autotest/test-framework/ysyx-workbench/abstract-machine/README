AbstractMachine is a minimal, modularized, and machine-independent 
abstraction layer of the computer hardware:

* physical memory and direct execution (The "Turing Machine");
* basic model for input and output devices (I/O Extension);
* interrupt/exception and processor context management (Context Extension);
* virtual memory and protection (Virtual Memory Extension);
* multiprocessing (Multiprocessing Extension).

CONTACTS

Bug reports and suggestions go to <PERSON><PERSON> (<EMAIL>) and <PERSON><PERSON><PERSON> (yuzih<PERSON>@ict.ac.cn).
