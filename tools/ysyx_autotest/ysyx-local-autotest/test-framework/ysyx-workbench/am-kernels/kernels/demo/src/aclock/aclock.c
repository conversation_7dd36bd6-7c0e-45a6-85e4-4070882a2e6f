/*
 * aclock - ascii clock for vt100 terminals
 * failsafe mode:
 * > no curses / termcap required
 * > no floating point required
 * > no ANSI C compiler required
 *
 * Copyright (c) 2002 <PERSON><PERSON> <<EMAIL>>
 * Version 1.8 (knr-nofloat-vt100); Dublin, June 2002
 *
 * Compilation: cc aclock-vt100.c -o aclock
 *
 * https://github.com/tenox7/aclock/blob/master/sources/aclock-vt100.c
 *
 * 2021-03-26    Meco Man   port to RT-Thread
 *
 */

#include <am.h>
#include <amdev.h>
#include <klib-macros.h>
#include <io.h>

static const int minute[60][8][2] = {
  { { 39,11 },{ 39,10 },{ 39,9 },{ 39,8 },{ 39,7 },{ 39,6 },{ 39,5 },{ 39,4 } },
  { { 40,11 },{ 40,10 },{ 40,9 },{ 40,8 },{ 41,7 },{ 41,6 },{ 41,5 },{ 41,4 } },
  { { 40,11 },{ 40,10 },{ 41,9 },{ 41,8 },{ 42,7 },{ 42,6 },{ 42,5 },{ 43,4 } },
  { { 40,11 },{ 41,10 },{ 41,9 },{ 42,8 },{ 43,7 },{ 43,6 },{ 44,5 },{ 44,4 } },
  { { 40,11 },{ 41,10 },{ 42,9 },{ 43,8 },{ 44,7 },{ 44,6 },{ 45,5 },{ 46,4 } },
  { { 40,11 },{ 41,10 },{ 42,9 },{ 43,8 },{ 44,7 },{ 45,6 },{ 46,5 },{ 47,5 } },
  { { 41,11 },{ 42,10 },{ 43,9 },{ 44,8 },{ 45,7 },{ 47,7 },{ 48,6 },{ 49,5 } },
  { { 41,11 },{ 42,10 },{ 44,9 },{ 45,9 },{ 46,8 },{ 48,7 },{ 49,6 },{ 50,6 } },
  { { 41,11 },{ 42,10 },{ 44,9 },{ 45,9 },{ 47,8 },{ 48,7 },{ 50,7 },{ 51,6 } },
  { { 41,11 },{ 43,10 },{ 44,10 },{ 46,9 },{ 48,9 },{ 49,8 },{ 51,7 },{ 52,7 } },
  { { 41,11 },{ 43,10 },{ 45,10 },{ 46,9 },{ 48,9 },{ 50,8 },{ 52,8 },{ 53,7 } },
  { { 41,11 },{ 43,11 },{ 45,10 },{ 47,10 },{ 49,9 },{ 50,9 },{ 52,9 },{ 54,8 } },
  { { 41,11 },{ 43,11 },{ 45,11 },{ 47,10 },{ 49,10 },{ 51,10 },{ 53,9 },{ 55,9 } },
  { { 41,11 },{ 43,11 },{ 45,11 },{ 47,11 },{ 49,10 },{ 51,10 },{ 53,10 },{ 55,10 } },
  { { 41,11 },{ 43,11 },{ 45,11 },{ 47,11 },{ 49,11 },{ 51,11 },{ 53,11 },{ 55,11 } },
  { { 42,12 },{ 44,12 },{ 46,12 },{ 48,12 },{ 50,12 },{ 52,12 },{ 54,12 },{ 56,12 } },
  { { 41,12 },{ 43,12 },{ 45,12 },{ 47,12 },{ 49,12 },{ 51,12 },{ 53,12 },{ 55,12 } },
  { { 41,12 },{ 43,12 },{ 45,12 },{ 47,12 },{ 49,13 },{ 51,13 },{ 53,13 },{ 55,13 } },
  { { 41,12 },{ 43,12 },{ 45,12 },{ 47,13 },{ 49,13 },{ 51,13 },{ 53,14 },{ 55,14 } },
  { { 41,12 },{ 43,12 },{ 45,13 },{ 47,13 },{ 49,14 },{ 50,14 },{ 52,14 },{ 54,15 } },
  { { 41,12 },{ 43,13 },{ 45,13 },{ 46,14 },{ 48,14 },{ 50,15 },{ 52,15 },{ 53,16 } },
  { { 41,12 },{ 43,13 },{ 44,13 },{ 46,14 },{ 48,14 },{ 49,15 },{ 51,16 },{ 52,16 } },
  { { 41,12 },{ 42,13 },{ 44,14 },{ 45,14 },{ 47,15 },{ 48,16 },{ 50,16 },{ 51,17 } },
  { { 41,12 },{ 42,13 },{ 44,14 },{ 45,14 },{ 46,15 },{ 48,16 },{ 49,17 },{ 50,17 } },
  { { 41,12 },{ 42,13 },{ 43,14 },{ 44,15 },{ 45,16 },{ 47,16 },{ 48,17 },{ 49,18 } },
  { { 40,12 },{ 41,13 },{ 42,14 },{ 43,15 },{ 44,16 },{ 45,17 },{ 46,18 },{ 47,18 } },
  { { 40,12 },{ 41,13 },{ 42,14 },{ 43,15 },{ 44,16 },{ 44,17 },{ 45,18 },{ 46,19 } },
  { { 40,12 },{ 41,13 },{ 41,14 },{ 42,15 },{ 43,16 },{ 43,17 },{ 44,18 },{ 44,19 } },
  { { 40,12 },{ 40,13 },{ 41,14 },{ 41,15 },{ 42,16 },{ 42,17 },{ 42,18 },{ 43,19 } },
  { { 40,12 },{ 40,13 },{ 40,14 },{ 40,15 },{ 41,16 },{ 41,17 },{ 41,18 },{ 41,19 } },
  { { 39,12 },{ 39,13 },{ 39,14 },{ 39,15 },{ 39,16 },{ 39,17 },{ 39,18 },{ 39,19 } },
  { { 39,12 },{ 39,13 },{ 39,14 },{ 39,15 },{ 38,16 },{ 38,17 },{ 38,18 },{ 38,19 } },
  { { 39,12 },{ 39,13 },{ 38,14 },{ 38,15 },{ 37,16 },{ 37,17 },{ 37,18 },{ 36,19 } },
  { { 39,12 },{ 38,13 },{ 38,14 },{ 37,15 },{ 36,16 },{ 36,17 },{ 35,18 },{ 35,19 } },
  { { 39,12 },{ 38,13 },{ 37,14 },{ 36,15 },{ 35,16 },{ 35,17 },{ 34,18 },{ 33,19 } },
  { { 38,12 },{ 37,13 },{ 36,14 },{ 35,15 },{ 34,16 },{ 33,17 },{ 32,18 },{ 31,18 } },
  { { 38,12 },{ 37,13 },{ 36,14 },{ 35,15 },{ 34,16 },{ 32,16 },{ 31,17 },{ 30,18 } },
  { { 38,12 },{ 37,13 },{ 35,14 },{ 34,14 },{ 33,15 },{ 31,16 },{ 30,17 },{ 29,17 } },
  { { 38,12 },{ 37,13 },{ 35,14 },{ 34,14 },{ 32,15 },{ 31,16 },{ 29,16 },{ 28,17 } },
  { { 38,12 },{ 36,13 },{ 35,13 },{ 33,14 },{ 31,14 },{ 30,15 },{ 28,16 },{ 27,16 } },
  { { 38,12 },{ 36,13 },{ 34,13 },{ 33,14 },{ 31,14 },{ 29,15 },{ 27,15 },{ 26,16 } },
  { { 38,12 },{ 36,12 },{ 34,13 },{ 32,13 },{ 30,14 },{ 29,14 },{ 27,14 },{ 25,15 } },
  { { 38,12 },{ 36,12 },{ 34,12 },{ 32,13 },{ 30,13 },{ 28,13 },{ 26,14 },{ 24,14 } },
  { { 38,12 },{ 36,12 },{ 34,12 },{ 32,12 },{ 30,13 },{ 28,13 },{ 26,13 },{ 24,13 } },
  { { 38,12 },{ 36,12 },{ 34,12 },{ 32,12 },{ 30,12 },{ 28,12 },{ 26,12 },{ 24,12 } },
  { { 38,11 },{ 36,11 },{ 34,11 },{ 32,11 },{ 30,11 },{ 28,11 },{ 26,11 },{ 24,11 } },
  { { 38,11 },{ 36,11 },{ 34,11 },{ 32,11 },{ 30,11 },{ 28,11 },{ 26,11 },{ 24,11 } },
  { { 38,11 },{ 36,11 },{ 34,11 },{ 32,11 },{ 30,10 },{ 28,10 },{ 26,10 },{ 24,10 } },
  { { 38,11 },{ 36,11 },{ 34,11 },{ 32,10 },{ 30,10 },{ 28,10 },{ 26,9 },{ 24,9 } },
  { { 38,11 },{ 36,11 },{ 34,10 },{ 32,10 },{ 30,9 },{ 29,9 },{ 27,9 },{ 25,8 } },
  { { 38,11 },{ 36,11 },{ 34,10 },{ 33,10 },{ 31,9 },{ 29,9 },{ 27,8 },{ 26,8 } },
  { { 38,11 },{ 36,10 },{ 35,10 },{ 33,9 },{ 31,9 },{ 30,8 },{ 28,7 },{ 27,7 } },
  { { 38,11 },{ 37,10 },{ 35,9 },{ 34,9 },{ 32,8 },{ 31,7 },{ 29,7 },{ 28,6 } },
  { { 38,11 },{ 37,10 },{ 35,9 },{ 34,9 },{ 33,8 },{ 31,7 },{ 30,6 },{ 29,6 } },
  { { 38,11 },{ 37,10 },{ 36,9 },{ 35,8 },{ 34,7 },{ 32,7 },{ 31,6 },{ 30,5 } },
  { { 39,11 },{ 38,10 },{ 37,9 },{ 36,8 },{ 35,7 },{ 34,6 },{ 33,5 },{ 32,5 } },
  { { 39,11 },{ 38,10 },{ 37,9 },{ 36,8 },{ 35,7 },{ 35,6 },{ 34,5 },{ 33,4 } },
  { { 39,11 },{ 38,10 },{ 38,9 },{ 37,8 },{ 36,7 },{ 36,6 },{ 35,5 },{ 35,4 } },
  { { 39,11 },{ 39,10 },{ 38,9 },{ 38,8 },{ 37,7 },{ 37,6 },{ 37,5 },{ 36,4 } },
  { { 39,11 },{ 39,10 },{ 39,9 },{ 39,8 },{ 38,7 },{ 38,6 },{ 38,5 },{ 38,4 } }
};

static const int hour[60][6][2] = {
  { { 39,11 },{ 39,10 },{ 39,9 },{ 39,8 },{ 39,7 },{ 39,6 } },
  { { 40,11 },{ 40,10 },{ 40,9 },{ 40,8 },{ 41,7 },{ 41,6 } },
  { { 40,11 },{ 40,10 },{ 41,9 },{ 41,8 },{ 42,7 },{ 42,6 } },
  { { 40,11 },{ 41,10 },{ 41,9 },{ 42,8 },{ 43,7 },{ 43,6 } },
  { { 40,11 },{ 41,10 },{ 42,9 },{ 43,8 },{ 44,7 },{ 44,6 } },
  { { 40,11 },{ 41,10 },{ 42,9 },{ 43,8 },{ 44,7 },{ 45,6 } },
  { { 41,11 },{ 42,10 },{ 43,9 },{ 44,8 },{ 45,7 },{ 47,7 } },
  { { 41,11 },{ 42,10 },{ 44,9 },{ 45,9 },{ 46,8 },{ 48,7 } },
  { { 41,11 },{ 42,10 },{ 44,9 },{ 45,9 },{ 47,8 },{ 48,7 } },
  { { 41,11 },{ 43,10 },{ 44,10 },{ 46,9 },{ 48,9 },{ 49,8 } },
  { { 41,11 },{ 43,10 },{ 45,10 },{ 46,9 },{ 48,9 },{ 50,8 } },
  { { 41,11 },{ 43,11 },{ 45,10 },{ 47,10 },{ 49,9 },{ 50,9 } },
  { { 41,11 },{ 43,11 },{ 45,11 },{ 47,10 },{ 49,10 },{ 51,10 } },
  { { 41,11 },{ 43,11 },{ 45,11 },{ 47,11 },{ 49,10 },{ 51,10 } },
  { { 41,11 },{ 43,11 },{ 45,11 },{ 47,11 },{ 49,11 },{ 51,11 } },
  { { 42,12 },{ 44,12 },{ 46,12 },{ 48,12 },{ 50,12 },{ 52,12 } },
  { { 41,12 },{ 43,12 },{ 45,12 },{ 47,12 },{ 49,12 },{ 51,12 } },
  { { 41,12 },{ 43,12 },{ 45,12 },{ 47,12 },{ 49,13 },{ 51,13 } },
  { { 41,12 },{ 43,12 },{ 45,12 },{ 47,13 },{ 49,13 },{ 51,13 } },
  { { 41,12 },{ 43,12 },{ 45,13 },{ 47,13 },{ 49,14 },{ 50,14 } },
  { { 41,12 },{ 43,13 },{ 45,13 },{ 46,14 },{ 48,14 },{ 50,15 } },
  { { 41,12 },{ 43,13 },{ 44,13 },{ 46,14 },{ 48,14 },{ 49,15 } },
  { { 41,12 },{ 42,13 },{ 44,14 },{ 45,14 },{ 47,15 },{ 48,16 } },
  { { 41,12 },{ 42,13 },{ 44,14 },{ 45,14 },{ 46,15 },{ 48,16 } },
  { { 41,12 },{ 42,13 },{ 43,14 },{ 44,15 },{ 45,16 },{ 47,16 } },
  { { 40,12 },{ 41,13 },{ 42,14 },{ 43,15 },{ 44,16 },{ 45,17 } },
  { { 40,12 },{ 41,13 },{ 42,14 },{ 43,15 },{ 44,16 },{ 44,17 } },
  { { 40,12 },{ 41,13 },{ 41,14 },{ 42,15 },{ 43,16 },{ 43,17 } },
  { { 40,12 },{ 40,13 },{ 41,14 },{ 41,15 },{ 42,16 },{ 42,17 } },
  { { 40,12 },{ 40,13 },{ 40,14 },{ 40,15 },{ 41,16 },{ 41,17 } },
  { { 39,12 },{ 39,13 },{ 39,14 },{ 39,15 },{ 39,16 },{ 39,17 } },
  { { 39,12 },{ 39,13 },{ 39,14 },{ 39,15 },{ 38,16 },{ 38,17 } },
  { { 39,12 },{ 39,13 },{ 38,14 },{ 38,15 },{ 37,16 },{ 37,17 } },
  { { 39,12 },{ 38,13 },{ 38,14 },{ 37,15 },{ 36,16 },{ 36,17 } },
  { { 39,12 },{ 38,13 },{ 37,14 },{ 36,15 },{ 35,16 },{ 35,17 } },
  { { 38,12 },{ 37,13 },{ 36,14 },{ 35,15 },{ 34,16 },{ 33,17 } },
  { { 38,12 },{ 37,13 },{ 36,14 },{ 35,15 },{ 34,16 },{ 32,16 } },
  { { 38,12 },{ 37,13 },{ 35,14 },{ 34,14 },{ 33,15 },{ 31,16 } },
  { { 38,12 },{ 37,13 },{ 35,14 },{ 34,14 },{ 32,15 },{ 31,16 } },
  { { 38,12 },{ 36,13 },{ 35,13 },{ 33,14 },{ 31,14 },{ 30,15 } },
  { { 38,12 },{ 36,13 },{ 34,13 },{ 33,14 },{ 31,14 },{ 29,15 } },
  { { 38,12 },{ 36,12 },{ 34,13 },{ 32,13 },{ 30,14 },{ 29,14 } },
  { { 38,12 },{ 36,12 },{ 34,12 },{ 32,13 },{ 30,13 },{ 28,13 } },
  { { 38,12 },{ 36,12 },{ 34,12 },{ 32,12 },{ 30,13 },{ 28,13 } },
  { { 38,12 },{ 36,12 },{ 34,12 },{ 32,12 },{ 30,12 },{ 28,12 } },
  { { 38,11 },{ 36,11 },{ 34,11 },{ 32,11 },{ 30,11 },{ 28,11 } },
  { { 38,11 },{ 36,11 },{ 34,11 },{ 32,11 },{ 30,11 },{ 28,11 } },
  { { 38,11 },{ 36,11 },{ 34,11 },{ 32,11 },{ 30,10 },{ 28,10 } },
  { { 38,11 },{ 36,11 },{ 34,11 },{ 32,10 },{ 30,10 },{ 28,10 } },
  { { 38,11 },{ 36,11 },{ 34,10 },{ 32,10 },{ 30,9 },{ 29,9 } },
  { { 38,11 },{ 36,11 },{ 34,10 },{ 33,10 },{ 31,9 },{ 29,9 } },
  { { 38,11 },{ 36,10 },{ 35,10 },{ 33,9 },{ 31,9 },{ 30,8 } },
  { { 38,11 },{ 37,10 },{ 35,9 },{ 34,9 },{ 32,8 },{ 31,7 } },
  { { 38,11 },{ 37,10 },{ 35,9 },{ 34,9 },{ 33,8 },{ 31,7 } },
  { { 38,11 },{ 37,10 },{ 36,9 },{ 35,8 },{ 34,7 },{ 32,7 } },
  { { 39,11 },{ 38,10 },{ 37,9 },{ 36,8 },{ 35,7 },{ 34,6 } },
  { { 39,11 },{ 38,10 },{ 37,9 },{ 36,8 },{ 35,7 },{ 35,6 } },
  { { 39,11 },{ 38,10 },{ 38,9 },{ 37,8 },{ 36,7 },{ 36,6 } },
  { { 39,11 },{ 39,10 },{ 38,9 },{ 38,8 },{ 37,7 },{ 37,6 } },
  { { 39,11 },{ 39,10 },{ 39,9 },{ 39,8 },{ 38,7 },{ 38,6 } }
};

static const int circle[60][3] = {
  { 62,12,111 }, { 61,13,46 }, { 61,14,46 }, { 60,15,46 },
  { 60,16,46 }, { 59,17,111 }, { 57,18,46 }, { 56,19,46 },
  { 54,20,46 }, { 52,20,46 }, { 51,21,111 }, { 48,22,46 },
  { 46,22,46 }, { 44,22,46 }, { 42,22,46 }, { 40,23,111 },
  { 37,22,46 }, { 35,22,46 }, { 33,22,46 }, { 31,22,46 },
  { 29,21,111 }, { 27,20,46 }, { 25,20,46 }, { 23,19,46 },
  { 22,18,46 }, { 20,17,111 }, { 19,16,46 }, { 19,15,46 },
  { 18,14,46 }, { 18,13,46 }, { 18,12,111 }, { 18,10,46 },
  { 18,9,46 }, { 19,8,46 }, { 19,7,46 }, { 20,6,111 },
  { 22,5,46 }, { 23,4,46 }, { 25,3,46 }, { 27,3,46 },
  { 28,2,111 }, { 31,1,46 }, { 33,1,46 }, { 35,1,46 },
  { 37,1,46 }, { 39,1,111 }, { 42,1,46 }, { 44,1,46 },
  { 46,1,46 }, { 48,1,46 }, { 51,2,111 }, { 52,3,46 },
  { 54,3,46 }, { 56,4,46 }, { 57,5,46 }, { 59,6,111 },
  { 60,7,46 }, { 60,8,46 }, { 61,9,46 }, { 61,10,46 }
};


static void draw_point(int x, int y, int c) {
  int xx = x - 16;
#ifdef HAS_GUI
  xx /= 2;
#endif
  print_char(c, y, xx);
}

//void draw_text(int x, int y, char *string) {
//  rt_kprintf("\033[%d;%dH%s", y, x, string);
//}

static void draw_circle(void) {
  int n;
  for(n=0;n<60;n++)
    draw_point(circle[n][0], circle[n][1], circle[n][2]);
}

static void draw_hour(int n) {
  int m;
  for(m=0;m<6;m++)
    draw_point(hour[n][m][0], hour[n][m][1], 'h');
}

static void draw_minute(int n) {
  int m;
  for(m=0;m<8;m++)
    draw_point(minute[n][m][0], minute[n][m][1], 'M');
}

static void draw_seconds(int n) {
  int m;
  for(m=0;m<8;m++)
    draw_point(minute[n][m][0], minute[n][m][1], '.');
}

void aclock(void) {
  while(1) {
    AM_TIMER_RTC_T rtc = io_read(AM_TIMER_RTC);
    screen_clear();
    draw_circle();
    draw_hour(((rtc.hour > 12 ? rtc.hour - 12 : rtc.hour) * 5) + (rtc.minute / 10));
    draw_minute(rtc.minute);
    draw_seconds(rtc.second);
    screen_refresh();
    //draw_text(35, 6, ".:ACLOCK:.");
    //rt_sprintf(digital_time, "[%02d:%02d:%02d]", ltime->tm_hour, ltime->tm_min, ltime->tm_sec);
    //draw_text(35, 19, digital_time);
    usleep(1000000);
  }
}
