{"displayTimeUnit":"ns","traceEvents":[{"pid":0,"ph":"M","args":{"name":"clangd"},"name":"process_name"},{"pid":0,"ph":"M","args":{"name":"clangd"},"name":"thread_name","tid":241390},{"pid":0,"ph":"i","args":{"Message":"clangd version 19.1.2 (https://github.com/llvm/llvm-project 7ba7d8e2f7b6445b60679da826210cdde29eaf8b)"},"name":"Log","tid":241390,"ts":128.535},{"pid":0,"ph":"i","args":{"Message":"Features: linux+grpc"},"name":"Log","tid":241390,"ts":243.535},{"pid":0,"ph":"i","args":{"Message":"PID: 241390"},"name":"Log","tid":241390,"ts":260.387},{"pid":0,"ph":"i","args":{"Message":"Working directory: /home/<USER>/Desktop/auto-test"},"name":"Log","tid":241390,"ts":308.12799999999999},{"pid":0,"ph":"i","args":{"Message":"argv[0]: /home/<USER>/.config/Code/User/globalStorage/llvm-vs-code-extensions.vscode-clangd/install/19.1.2/clangd_19.1.2/bin/clangd"},"name":"Log","tid":241390,"ts":329.81},{"pid":0,"ph":"i","args":{"Message":"Starting LSP over stdin/stdout"},"name":"Log","tid":241390,"ts":406.80599999999998},{"pid":0,"ph":"i","args":{"Message":"<-- initialize(0)"},"name":"Log","tid":241390,"ts":1672.211},{"pid":0,"ph":"i","args":{"Message":"--> reply:initialize(0) 1 ms"},"name":"Log","tid":241390,"ts":3141.3850000000002},{"pid":0,"ph":"X","args":{"Params":{"capabilities":{"general":{"markdown":{"parser":"marked","version":"1.1.0"},"positionEncodings":["utf-16"],"regularExpressions":{"engine":"ECMAScript","version":"ES2020"},"staleRequestSupport":{"cancel":true,"retryOnContentModified":["textDocument/semanticTokens/full","textDocument/semanticTokens/range","textDocument/semanticTokens/full/delta"]}},"notebookDocument":{"synchronization":{"dynamicRegistration":true,"executionSummarySupport":true}},"textDocument":{"callHierarchy":{"dynamicRegistration":true},"codeAction":{"codeActionLiteralSupport":{"codeActionKind":{"valueSet":["","quickfix","refactor","refactor.extract","refactor.inline","refactor.rewrite","source","source.organizeImports"]}},"dataSupport":true,"disabledSupport":true,"dynamicRegistration":true,"honorsChangeAnnotations":true,"isPreferredSupport":true,"resolveSupport":{"properties":["edit"]}},"codeLens":{"dynamicRegistration":true},"colorProvider":{"dynamicRegistration":true},"completion":{"completionItem":{"commitCharactersSupport":true,"deprecatedSupport":true,"documentationFormat":["markdown","plaintext"],"insertReplaceSupport":true,"insertTextModeSupport":{"valueSet":[1,2]},"labelDetailsSupport":true,"preselectSupport":true,"resolveSupport":{"properties":["documentation","detail","additionalTextEdits"]},"snippetSupport":true,"tagSupport":{"valueSet":[1]}},"completionItemKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25]},"completionList":{"itemDefaults":["commitCharacters","editRange","insertTextFormat","insertTextMode","data"]},"contextSupport":true,"dynamicRegistration":true,"editsNearCursor":true,"insertTextMode":2},"declaration":{"dynamicRegistration":true,"linkSupport":true},"definition":{"dynamicRegistration":true,"linkSupport":true},"diagnostic":{"dynamicRegistration":true,"relatedDocumentSupport":false},"documentHighlight":{"dynamicRegistration":true},"documentLink":{"dynamicRegistration":true,"tooltipSupport":true},"documentSymbol":{"dynamicRegistration":true,"hierarchicalDocumentSymbolSupport":true,"labelSupport":true,"symbolKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]},"tagSupport":{"valueSet":[1]}},"foldingRange":{"dynamicRegistration":true,"foldingRange":{"collapsedText":false},"foldingRangeKind":{"valueSet":["comment","imports","region"]},"lineFoldingOnly":true,"rangeLimit":5000},"formatting":{"dynamicRegistration":true},"hover":{"contentFormat":["markdown","plaintext"],"dynamicRegistration":true},"implementation":{"dynamicRegistration":true,"linkSupport":true},"inactiveRegionsCapabilities":{"inactiveRegions":true},"inlayHint":{"dynamicRegistration":true,"resolveSupport":{"properties":["tooltip","textEdits","label.tooltip","label.location","label.command"]}},"inlineValue":{"dynamicRegistration":true},"linkedEditingRange":{"dynamicRegistration":true},"onTypeFormatting":{"dynamicRegistration":true},"publishDiagnostics":{"codeDescriptionSupport":true,"dataSupport":true,"relatedInformation":true,"tagSupport":{"valueSet":[1,2]},"versionSupport":false},"rangeFormatting":{"dynamicRegistration":true,"rangesSupport":true},"references":{"dynamicRegistration":true},"rename":{"dynamicRegistration":true,"honorsChangeAnnotations":true,"prepareSupport":true,"prepareSupportDefaultBehavior":1},"selectionRange":{"dynamicRegistration":true},"semanticTokens":{"augmentsSyntaxTokens":true,"dynamicRegistration":true,"formats":["relative"],"multilineTokenSupport":false,"overlappingTokenSupport":false,"requests":{"full":{"delta":true},"range":true},"serverCancelSupport":true,"tokenModifiers":["declaration","definition","readonly","static","deprecated","abstract","async","modification","documentation","defaultLibrary"],"tokenTypes":["namespace","type","class","enum","interface","struct","typeParameter","parameter","variable","property","enumMember","event","function","method","macro","keyword","modifier","comment","string","number","regexp","operator","decorator"]},"signatureHelp":{"contextSupport":true,"dynamicRegistration":true,"signatureInformation":{"activeParameterSupport":true,"documentationFormat":["markdown","plaintext"],"parameterInformation":{"labelOffsetSupport":true}}},"synchronization":{"didSave":true,"dynamicRegistration":true,"willSave":true,"willSaveWaitUntil":true},"typeDefinition":{"dynamicRegistration":true,"linkSupport":true},"typeHierarchy":{"dynamicRegistration":true}},"window":{"showDocument":{"support":true},"showMessage":{"messageActionItem":{"additionalPropertiesSupport":true}},"workDoneProgress":true},"workspace":{"applyEdit":true,"codeLens":{"refreshSupport":true},"configuration":true,"diagnostics":{"refreshSupport":true},"didChangeConfiguration":{"dynamicRegistration":true},"didChangeWatchedFiles":{"dynamicRegistration":true,"relativePatternSupport":true},"executeCommand":{"dynamicRegistration":true},"fileOperations":{"didCreate":true,"didDelete":true,"didRename":true,"dynamicRegistration":true,"willCreate":true,"willDelete":true,"willRename":true},"foldingRange":{"refreshSupport":true},"inlayHint":{"refreshSupport":true},"inlineValue":{"refreshSupport":true},"semanticTokens":{"refreshSupport":true},"symbol":{"dynamicRegistration":true,"resolveSupport":{"properties":["location.range"]},"symbolKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]},"tagSupport":{"valueSet":[1]}},"workspaceEdit":{"changeAnnotationSupport":{"groupsOnLabel":true},"documentChanges":true,"failureHandling":"textOnlyTransactional","normalizesLineEndings":true,"resourceOperations":["create","rename","delete"]},"workspaceFolders":true}},"clientInfo":{"name":"Visual Studio Code","version":"1.100.3"},"initializationOptions":{"clangdFileStatus":true,"fallbackFlags":["-I/home/<USER>/Desktop/ysyx-workbench/nemu/include","-I/home/<USER>/Desktop/ysyx-workbench/nemu/src/isa/riscv32/include/","-I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include/","-I/home/<USER>/Desktop/ysyx-workbench/npc/build/obj_dir/","-I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/klib/include","-I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/src/platform/nemu/include","-I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include","-I/home/<USER>/Desktop/ysyx-workbench/nvboard/include/","-I/usr/include/SDL2/","-I/usr/share/verilator/include/","-I/home/<USER>/Desktop/ysyx-workbench/npc/csrc/include"]},"locale":"en","processId":241256,"rootPath":"/home/<USER>/Desktop/auto-test","rootUri":"file:///home/<USER>/Desktop/auto-test","trace":"off","workspaceFolders":[{"name":"auto-test","uri":"file:///home/<USER>/Desktop/auto-test"}]},"Reply":{"capabilities":{"astProvider":true,"callHierarchyProvider":true,"clangdInlayHintsProvider":true,"codeActionProvider":{"codeActionKinds":["quickfix","refactor","info"]},"compilationDatabase":{"automaticReload":true},"completionProvider":{"resolveProvider":false,"triggerCharacters":[".","<",">",":","\"","/","*"]},"declarationProvider":true,"definitionProvider":true,"documentFormattingProvider":true,"documentHighlightProvider":true,"documentLinkProvider":{"resolveProvider":false},"documentOnTypeFormattingProvider":{"firstTriggerCharacter":"\n","moreTriggerCharacter":[]},"documentRangeFormattingProvider":true,"documentSymbolProvider":true,"executeCommandProvider":{"commands":["clangd.applyFix","clangd.applyRename","clangd.applyTweak"]},"foldingRangeProvider":true,"hoverProvider":true,"implementationProvider":true,"inactiveRegionsProvider":true,"inlayHintProvider":true,"memoryUsageProvider":true,"referencesProvider":true,"renameProvider":{"prepareProvider":true},"selectionRangeProvider":true,"semanticTokensProvider":{"full":{"delta":true},"legend":{"tokenModifiers":["declaration","definition","deprecated","deduced","readonly","static","abstract","virtual","dependentName","defaultLibrary","usedAsMutableReference","usedAsMutablePointer","constructorOrDestructor","userDefined","functionScope","classScope","fileScope","globalScope"],"tokenTypes":["variable","variable","parameter","function","method","function","property","variable","class","interface","enum","enumMember","type","type","unknown","namespace","typeParameter","concept","type","macro","modifier","operator","bracket","label","comment"]},"range":false},"signatureHelpProvider":{"triggerCharacters":["(",")","{","}","<",">",","]},"standardTypeHierarchyProvider":true,"textDocumentSync":{"change":2,"openClose":true,"save":true},"typeDefinitionProvider":true,"typeHierarchyProvider":true,"workspaceSymbolProvider":true},"serverInfo":{"name":"clangd","version":"clangd version 19.1.2 (https://github.com/llvm/llvm-project 7ba7d8e2f7b6445b60679da826210cdde29eaf8b) linux+grpc x86_64-unknown-linux-gnu"}}},"dur":2358.3209999999999,"name":"initialize","tid":241390,"ts":1133.7840000000001},{"pid":0,"ph":"i","args":{"Message":"<-- initialized"},"name":"Log","tid":241390,"ts":6380.598},{"pid":0,"ph":"X","args":{"Params":{}},"dur":41.568999999999505,"name":"initialized","tid":241390,"ts":6369.4470000000001},{"pid":0,"ph":"i","args":{"Message":"<-- $/setTrace"},"name":"Log","tid":241390,"ts":151765.133},{"pid":0,"ph":"i","args":{"Message":"unhandled notification $/setTrace"},"name":"Log","tid":241390,"ts":151841.91},{"pid":0,"ph":"X","args":{"Params":{"value":"off"}},"dur":115.8410000000149,"name":"$/setTrace","tid":241390,"ts":151739.25399999999},{"pid":0,"ph":"i","args":{"Message":"<-- $/setTrace"},"name":"Log","tid":241390,"ts":1018182.722},{"pid":0,"ph":"i","args":{"Message":"unhandled notification $/setTrace"},"name":"Log","tid":241390,"ts":1018246.6040000001},{"pid":0,"ph":"X","args":{"Params":{"value":"off"}},"dur":101.12300000002142,"name":"$/setTrace","tid":241390,"ts":1018159.417},{"pid":0,"ph":"i","args":{"Message":"<-- $/setTrace"},"name":"Log","tid":241390,"ts":2028252.618},{"pid":0,"ph":"i","args":{"Message":"unhandled notification $/setTrace"},"name":"Log","tid":241390,"ts":2028289.669},{"pid":0,"ph":"X","args":{"Params":{"value":"off"}},"dur":61.146999999880791,"name":"$/setTrace","tid":241390,"ts":2028239.5430000001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/didOpen"},"name":"Log","tid":241390,"ts":274367516.90100002},{"pid":0,"ph":"X","args":{},"dur":158.70300000905991,"name":"AdjustCompileFlags","tid":241390,"ts":274367586.384},{"pid":0,"ph":"X","args":{},"dur":17.985000014305115,"name":"ProfileBrief","tid":241390,"ts":274367929.81900001},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.9470000267028809,"name":"Queued:Update","tid":241390,"ts":274367903.94},{"pid":0,"ph":"M","args":{"name":"TWorker:log.cpp"},"name":"thread_name","tid":250166},{"pid":0,"ph":"s","cat":"mock_cat","id":0,"name":"Context crosses threads","tid":241390,"ts":274367500.85000002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":0,"name":"Context crosses threads","tid":250166,"ts":274368186.76999998},{"pid":0,"ph":"X","args":{"ConfigFile":"/home/<USER>/.config/clangd/config.yaml"},"dur":5.2400000095367432,"name":"ConfigCompile","tid":250166,"ts":274368484.65799999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":307.94799995422363,"name":"getConfig","tid":250166,"ts":274368193.39200002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/publishDiagnostics"},"name":"Log","tid":250166,"ts":274368552.08700001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":5.0799999833106995,"name":"getConfig","tid":250166,"ts":274368590.50999999},{"pid":0,"ph":"i","args":{"Message":"Failed to find compilation database for /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"name":"Log","tid":250166,"ts":274368784.06},{"pid":0,"ph":"X","args":{},"dur":43.402999997138977,"name":"AdjustCompileFlags","tid":250166,"ts":274368799.61900002},{"pid":0,"ph":"i","args":{"Message":"ASTWorker building file /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp version 1 with command clangd fallback\n[/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils]\n/usr/lib/llvm-14/bin/clang -I/home/<USER>/Desktop/ysyx-workbench/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/nemu/src/isa/riscv32/include/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/build/obj_dir/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/klib/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/src/platform/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include -I/home/<USER>/Desktop/ysyx-workbench/nvboard/include/ -I/usr/include/SDL2/ -I/usr/share/verilator/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/csrc/include -include=/headers/file.h -I/other/headers -resource-dir=/home/<USER>/.config/Code/User/globalStorage/llvm-vs-code-extensions.vscode-clangd/install/19.1.2/clangd_19.1.2/lib/clang/19 -- /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"name":"Log","tid":250166,"ts":274368869.09200001},{"pid":0,"ph":"M","args":{"name":"eWorker:log.cpp"},"name":"thread_name","tid":250167},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250167,"ts":274370075.14399999},{"pid":0,"ph":"s","cat":"mock_cat","id":1,"name":"Context crosses threads","tid":250166,"ts":274368159.21700001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":1,"name":"Context crosses threads","tid":250167,"ts":274370174.25300002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(1)"},"name":"Log","tid":241390,"ts":274380875.28799999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentSymbol(2)"},"name":"Log","tid":241390,"ts":274381935.01999998},{"pid":0,"ph":"i","args":{"Message":"Built preamble of size 550412 for file /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp version 1 in 0.04 seconds"},"name":"Log","tid":250167,"ts":274406459.24800003},{"pid":0,"ph":"X","args":{},"dur":176.57699996232986,"name":"Running PreambleCallback","tid":250167,"ts":274406514.19300002},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":36544.680999994278,"name":"BuildPreamble","tid":250167,"ts":274370157.63099998},{"pid":0,"ph":"M","args":{"name":"/utils/log.cpp1"},"name":"thread_name","tid":250169},{"pid":0,"ph":"i","args":{"Message":"--> workspace/semanticTokens/refresh(0)"},"name":"Log","tid":250167,"ts":274406755.47299999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":29.897000014781952,"name":"getConfig","tid":250166,"ts":274406828.03200001},{"pid":0,"ph":"i","args":{"Message":"<-- reply(0)"},"name":"Log","tid":241390,"ts":274408639.15799999},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":3072.1129999756813,"name":"CreatePreamblePatch","tid":250166,"ts":274406955.47500002},{"pid":0,"ph":"X","args":{},"dur":291.98800003528595,"name":"ClangTidyOpts","tid":250166,"ts":274410138.27899998},{"pid":0,"ph":"X","args":{},"dur":1818.8420000076294,"name":"ClangTidyInit","tid":250166,"ts":274411092.38999999},{"pid":0,"ph":"X","args":{},"dur":470.908999979496,"name":"ClangTidyMatch","tid":250166,"ts":274415705.97600001},{"pid":0,"ph":"X","args":{},"dur":1.7129999995231628,"name":"IncludeCleaner::getUnused","tid":250166,"ts":274416455.64700001},{"pid":0,"ph":"X","args":{},"dur":140.46799999475479,"name":"include_cleaner::walkUsed","tid":250166,"ts":274416326.17000002},{"pid":0,"ph":"X","args":{},"dur":295.22399997711182,"name":"IncludeCleaner::issueIncludeCleanerDiagnostics","tid":250166,"ts":274416474.27200001},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":10000.656999945641,"name":"BuildAST","tid":250166,"ts":274406894.41900003},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/publishDiagnostics"},"name":"Log","tid":250166,"ts":274417281.91399997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/inactiveRegions"},"name":"Log","tid":250166,"ts":274417352.60900003},{"pid":0,"ph":"X","args":{},"dur":144.67599999904633,"name":"ASTSignals::derive","tid":250166,"ts":274417383.44800001},{"pid":0,"ph":"X","args":{},"dur":597.37999999523163,"name":"Running main AST callback","tid":250166,"ts":274416940.88300002},{"pid":0,"ph":"X","args":{},"dur":10728.416000008583,"name":"Build AST","tid":250166,"ts":274406822.171},{"pid":0,"ph":"X","args":{},"dur":38645.671000003815,"name":"Update","tid":250166,"ts":274368159.21700001},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"languageId":"cpp","text":"/***************************************************************************************\n* Copyright (c) 2014-2022 Zihao Yu, Nanjing University\n*\n* NEMU is licensed under Mulan PSL v2.\n* You can use this software according to the terms and conditions of the Mulan PSL v2.\n* You may obtain a copy of Mulan PSL v2 at:\n*          http://license.coscl.org.cn/MulanPSL2\n*\n* THIS SOFTWARE IS PROVIDED ON AN \"AS IS\" BASIS, WITHOUT WARRANTIES OF ANY KIND,\n* EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,\n* MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.\n*\n* See the Mulan PSL v2 for more details.\n***************************************************************************************/\n\n#include <common.h>\n\nextern uint64_t g_nr_guest_inst;\nFILE *log_fp = NULL;\n\nvoid init_log(const char *log_file) {\n  log_fp = stdout;\n  if (log_file != NULL) {\n    FILE *fp = fopen(log_file, \"w\");\n    Assert(fp, \"Can not open '%s'\", log_file);\n    log_fp = fp;\n  }\n  Log(\"Log is written to %s\", log_file ? log_file : \"stdout\");\n}\n\nbool log_enable() {\n#ifdef CONFIG_ITRACE_INF\n    return true;\n#else \n    return MUXDEF(CONFIG_TRACE, (g_nr_guest_inst <= CONFIG_TRACE_END), false);\n#endif\n}\n","uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp","version":1}}},"dur":631.19499999284744,"name":"textDocument/didOpen","tid":241390,"ts":274367500.85000002},{"pid":0,"ph":"X","args":{"CurrentRequest":"Update","PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.3050000071525574,"name":"Queued:codeAction","tid":241390,"ts":274380918.09899998},{"pid":0,"ph":"s","cat":"mock_cat","id":2,"name":"Context crosses threads","tid":241390,"ts":274380845.43099999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":2,"name":"Context crosses threads","tid":250166,"ts":274417610.89200002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":16.221000015735626,"name":"getConfig","tid":250166,"ts":274417616.42199999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(1) 36 ms"},"name":"Log","tid":250166,"ts":274417702.28600001},{"pid":0,"ph":"X","args":{},"dur":127.16299998760223,"name":"codeAction","tid":250166,"ts":274417603.78799999},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":0,"line":0},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[]},"dur":78.449000000953674,"name":"textDocument/codeAction","tid":241390,"ts":274380845.43099999},{"pid":0,"ph":"X","args":{"CurrentRequest":"Update","PreambleRequestsNames":[],"RequestsNames":["codeAction"]},"dur":3.3370000123977661,"name":"Queued:DocumentSymbols","tid":241390,"ts":274381972.23100001},{"pid":0,"ph":"s","cat":"mock_cat","id":3,"name":"Context crosses threads","tid":241390,"ts":274381921.55500001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":3,"name":"Context crosses threads","tid":250166,"ts":274417827.62599999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":5.300000011920929,"name":"getConfig","tid":250166,"ts":274417832.38499999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentSymbol(2) 35 ms"},"name":"Log","tid":250166,"ts":274417932.21499997},{"pid":0,"ph":"X","args":{},"dur":218.0359999537468,"name":"DocumentSymbols","tid":250166,"ts":274417820.94300002},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"detail":"uint64_t","kind":13,"name":"g_nr_guest_inst","range":{"end":{"character":31,"line":17},"start":{"character":0,"line":17}},"selectionRange":{"end":{"character":31,"line":17},"start":{"character":16,"line":17}}},{"detail":"FILE *","kind":13,"name":"log_fp","range":{"end":{"character":19,"line":18},"start":{"character":0,"line":18}},"selectionRange":{"end":{"character":12,"line":18},"start":{"character":6,"line":18}}},{"detail":"void (const char *)","kind":12,"name":"init_log","range":{"end":{"character":1,"line":28},"start":{"character":0,"line":20}},"selectionRange":{"end":{"character":13,"line":20},"start":{"character":5,"line":20}}},{"detail":"bool ()","kind":12,"name":"log_enable","range":{"end":{"character":1,"line":36},"start":{"character":0,"line":30}},"selectionRange":{"end":{"character":15,"line":30},"start":{"character":5,"line":30}}}]},"dur":56.097000002861023,"name":"textDocument/documentSymbol","tid":241390,"ts":274381921.55500001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":274418095.61699998},{"pid":0,"ph":"X","args":{},"dur":15361.72000002861,"name":"PreambleIndexing","tid":250169,"ts":274406732.94999999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(3)"},"name":"Log","tid":241390,"ts":274424704.20099998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":5.2699999809265137,"name":"Queued:SemanticHighlights","tid":241390,"ts":274424781.338},{"pid":0,"ph":"s","cat":"mock_cat","id":4,"name":"Context crosses threads","tid":241390,"ts":274424674.52399999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":4,"name":"Context crosses threads","tid":250166,"ts":274424846.32200003},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":27.72299998998642,"name":"getConfig","tid":250166,"ts":274424853.736},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(3) 0 ms"},"name":"Log","tid":250166,"ts":274425115.95599997},{"pid":0,"ph":"X","args":{},"dur":328.67800003290176,"name":"SemanticHighlights","tid":250166,"ts":274424835.58099997},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":{"data":[17,7,8,18,66048,0,9,15,0,131073,1,0,4,8,66048,0,6,6,0,131075,0,9,4,19,131072,2,5,8,3,131075,0,21,8,2,16403,1,2,6,0,131072,0,7,1,21,0,0,2,6,19,131072,1,6,8,2,16400,0,9,2,21,0,0,3,4,19,131072,1,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,6,8,2,16400,1,4,6,19,131072,0,7,2,1,16384,0,25,8,2,16400,1,4,6,0,131072,0,7,1,21,0,0,2,2,1,16384,2,2,3,19,131072,0,28,8,2,16400,0,9,1,21,0,0,2,8,2,16400,0,9,1,21,0,3,5,10,3,131075,1,7,17,19,131072,3,11,6,19,131072,0,7,12,19,131072,0,15,15,0,131072,0,16,2,21,0,0,3,16,19,131072],"resultId":"1"}},"dur":119.62800002098083,"name":"textDocument/semanticTokens/full","tid":241390,"ts":274424674.52399999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":274425240.13300002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentLink(4)"},"name":"Log","tid":241390,"ts":274427199.07200003},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":4.8089999556541443,"name":"Queued:DocumentLinks","tid":241390,"ts":274427248.08600003},{"pid":0,"ph":"s","cat":"mock_cat","id":5,"name":"Context crosses threads","tid":241390,"ts":274427174.47500002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":5,"name":"Context crosses threads","tid":250166,"ts":274427294.875},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":14.046999990940094,"name":"getConfig","tid":250166,"ts":274427300.55599999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentLink(4) 0 ms"},"name":"Log","tid":250166,"ts":274427371.111},{"pid":0,"ph":"X","args":{},"dur":126.84299999475479,"name":"DocumentLinks","tid":250166,"ts":274427287.11000001},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"range":{"end":{"character":19,"line":15},"start":{"character":9,"line":15}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/common.h"}]},"dur":82.83899998664856,"name":"textDocument/documentLink","tid":241390,"ts":274427174.47500002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":274427447.14600003},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(5)"},"name":"Log","tid":241390,"ts":274950847.15100002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.1940000057220459,"name":"Queued:InlayHints","tid":241390,"ts":274950898.05900002},{"pid":0,"ph":"s","cat":"mock_cat","id":6,"name":"Context crosses threads","tid":241390,"ts":274950830.079},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":6,"name":"Context crosses threads","tid":250166,"ts":274950937.12300003},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":10.651000022888184,"name":"getConfig","tid":250166,"ts":274950941.99199998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(5) 0 ms"},"name":"Log","tid":250166,"ts":274951059.07599998},{"pid":0,"ph":"X","args":{},"dur":169.80400002002716,"name":"InlayHints","tid":250166,"ts":274950930.12},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":0,"line":37},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":23}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":31,"line":23}}]},"dur":75.304000020027161,"name":"textDocument/inlayHint","tid":241390,"ts":274950830.079},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":274951130.72299999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(6)"},"name":"Log","tid":241390,"ts":274951624.60500002},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250184},{"pid":0,"ph":"X","args":{},"dur":12.793999969959259,"name":"WaitForFreeSemaphoreSlot","tid":250184,"ts":274951722.39200002},{"pid":0,"ph":"s","cat":"mock_cat","id":7,"name":"Context crosses threads","tid":241390,"ts":274951612.27200001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":7,"name":"Context crosses threads","tid":250184,"ts":274951781.44499999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":16.510999977588654,"name":"getConfig","tid":250184,"ts":274951775.20300001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(6) 0 ms"},"name":"Log","tid":250184,"ts":274951911.06199998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":82.196999967098236,"name":"textDocument/foldingRange","tid":241390,"ts":274951612.27200001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(7)"},"name":"Log","tid":241390,"ts":274953549.93000001},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.9039999842643738,"name":"Queued:codeAction","tid":241390,"ts":274953587.70200002},{"pid":0,"ph":"s","cat":"mock_cat","id":8,"name":"Context crosses threads","tid":241390,"ts":274953533.759},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":8,"name":"Context crosses threads","tid":250166,"ts":274953624.66299999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(8)"},"name":"Log","tid":241390,"ts":274953625.03299999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":13.375,"name":"getConfig","tid":250166,"ts":274953631.41600001},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250185},{"pid":0,"ph":"X","args":{},"dur":11.712999999523163,"name":"WaitForFreeSemaphoreSlot","tid":250185,"ts":274953734.45200002},{"pid":0,"ph":"s","cat":"mock_cat","id":9,"name":"Context crosses threads","tid":241390,"ts":274953612.60000002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":9,"name":"Context crosses threads","tid":250185,"ts":274953762.34600002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":16.35099995136261,"name":"getConfig","tid":250185,"ts":274953754.59100002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(7) 0 ms"},"name":"Log","tid":250166,"ts":274953789.347},{"pid":0,"ph":"X","args":{},"dur":209.01899999380112,"name":"codeAction","tid":250166,"ts":274953618.17000002},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":0,"line":28},"start":{"character":0,"line":28}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[]},"dur":62.408999979496002,"name":"textDocument/codeAction","tid":241390,"ts":274953533.759},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":274953862.87699997},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(8) 0 ms"},"name":"Log","tid":250185,"ts":274953910.72899997},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":105.01999998092651,"name":"textDocument/foldingRange","tid":241390,"ts":274953612.60000002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(9)"},"name":"Log","tid":241390,"ts":275168524.03500003},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250191},{"pid":0,"ph":"X","args":{},"dur":15.468999981880188,"name":"WaitForFreeSemaphoreSlot","tid":250191,"ts":275168642.73199999},{"pid":0,"ph":"s","cat":"mock_cat","id":10,"name":"Context crosses threads","tid":241390,"ts":275168507.24299997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":10,"name":"Context crosses threads","tid":250191,"ts":275168680.083},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":23.976000010967255,"name":"getConfig","tid":250191,"ts":275168670.815},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(9) 0 ms"},"name":"Log","tid":250191,"ts":275168853.11299998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":106.59400004148483,"name":"textDocument/foldingRange","tid":241390,"ts":275168507.24299997},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(10)"},"name":"Log","tid":241390,"ts":275251541.44499999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.4239999651908875,"name":"Queued:codeAction","tid":241390,"ts":275251582.81400001},{"pid":0,"ph":"s","cat":"mock_cat","id":11,"name":"Context crosses threads","tid":241390,"ts":275251522.19800001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":11,"name":"Context crosses threads","tid":250166,"ts":275251625.90600002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":11.171000003814697,"name":"getConfig","tid":250166,"ts":275251631.18599999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(10) 0 ms"},"name":"Log","tid":250166,"ts":275251736.25700003},{"pid":0,"ph":"X","args":{},"dur":152.76099997758865,"name":"codeAction","tid":250166,"ts":275251618.45200002},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":0,"line":28},"start":{"character":0,"line":28}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[]},"dur":70.935999989509583,"name":"textDocument/codeAction","tid":241390,"ts":275251522.19800001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":275251804.24599999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(11)"},"name":"Log","tid":241390,"ts":275335816.34399998},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250198},{"pid":0,"ph":"X","args":{},"dur":12.324000000953674,"name":"WaitForFreeSemaphoreSlot","tid":250198,"ts":275335916.73500001},{"pid":0,"ph":"s","cat":"mock_cat","id":12,"name":"Context crosses threads","tid":241390,"ts":275335798.199},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":12,"name":"Context crosses threads","tid":250198,"ts":275335948.24599999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":23.465000033378601,"name":"getConfig","tid":250198,"ts":275335939.76899999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(11) 0 ms"},"name":"Log","tid":250198,"ts":275336077.111},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":100.02100002765656,"name":"textDocument/foldingRange","tid":241390,"ts":275335798.199},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(12)"},"name":"Log","tid":241390,"ts":275515410.68300003},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.3349999785423279,"name":"Queued:SemanticHighlights","tid":241390,"ts":275515446.62099999},{"pid":0,"ph":"s","cat":"mock_cat","id":13,"name":"Context crosses threads","tid":241390,"ts":275515398.94},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":13,"name":"Context crosses threads","tid":250166,"ts":275515482.389},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":9.0669999718666077,"name":"getConfig","tid":250166,"ts":275515486.75800002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(12) 0 ms"},"name":"Log","tid":250166,"ts":275515624.71100003},{"pid":0,"ph":"X","args":{},"dur":185.43400001525879,"name":"SemanticHighlights","tid":250166,"ts":275515476.21799999},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":{"data":[17,7,8,18,66048,0,9,15,0,131073,1,0,4,8,66048,0,6,6,0,131075,0,9,4,19,131072,2,5,8,3,131075,0,21,8,2,16403,1,2,6,0,131072,0,7,1,21,0,0,2,6,19,131072,1,6,8,2,16400,0,9,2,21,0,0,3,4,19,131072,1,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,6,8,2,16400,1,4,6,19,131072,0,7,2,1,16384,0,25,8,2,16400,1,4,6,0,131072,0,7,1,21,0,0,2,2,1,16384,2,2,3,19,131072,0,28,8,2,16400,0,9,1,21,0,0,2,8,2,16400,0,9,1,21,0,3,5,10,3,131075,1,7,17,19,131072,3,11,6,19,131072,0,7,12,19,131072,0,15,15,0,131072,0,16,2,21,0,0,3,16,19,131072],"resultId":"2"}},"dur":53.962999999523163,"name":"textDocument/semanticTokens/full","tid":241390,"ts":275515398.94},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":275515687.25999999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(13)"},"name":"Log","tid":241390,"ts":275664998.69},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.8730000257492065,"name":"Queued:codeAction","tid":241390,"ts":275665034.509},{"pid":0,"ph":"s","cat":"mock_cat","id":14,"name":"Context crosses threads","tid":241390,"ts":275664984.07200003},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":14,"name":"Context crosses threads","tid":250166,"ts":275665069.27499998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":8.0649999976158142,"name":"getConfig","tid":250166,"ts":275665073.72399998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(13) 0 ms"},"name":"Log","tid":250166,"ts":275665306.47799999},{"pid":0,"ph":"X","args":{},"dur":297.51800000667572,"name":"codeAction","tid":250166,"ts":275665063.22299999},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":2,"line":27},"start":{"character":2,"line":27}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp","selection":{"end":{"character":2,"line":27},"start":{"character":2,"line":27}},"tweakID":"ExpandMacro"}],"command":"clangd.applyTweak","title":"Expand macro 'Log'"},"kind":"refactor","title":"Expand macro 'Log'"},{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp","selection":{"end":{"character":2,"line":27},"start":{"character":2,"line":27}},"tweakID":"ExtractFunction"}],"command":"clangd.applyTweak","title":"Extract to function"},"kind":"refactor","title":"Extract to function"}]},"dur":57.239999949932098,"name":"textDocument/codeAction","tid":241390,"ts":275664984.07200003},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":275665419.00300002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/didChange"},"name":"Log","tid":241390,"ts":276654653.75300002},{"pid":0,"ph":"s","cat":"mock_cat","id":15,"name":"Context crosses threads","tid":241390,"ts":276654637.20099998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":15,"name":"Context crosses threads","tid":250166,"ts":276654730.94999999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":276654761.70899999},{"pid":0,"ph":"X","args":{"next_request":"Update","sleep_ms":49},"dur":50047.191999971867,"name":"Debounce","tid":250166,"ts":276654717.384},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.1650000214576721,"name":"Queued:Update","tid":241390,"ts":276654697.08499998},{"pid":0,"ph":"s","cat":"mock_cat","id":16,"name":"Context crosses threads","tid":241390,"ts":276654637.20099998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":16,"name":"Context crosses threads","tid":250166,"ts":276704802.15799999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":9.1669999957084656,"name":"getConfig","tid":250166,"ts":276704806.79699999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":4.3079999685287476,"name":"getConfig","tid":250166,"ts":276704824.23000002},{"pid":0,"ph":"i","args":{"Message":"Failed to find compilation database for /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"name":"Log","tid":250166,"ts":276704843.87699997},{"pid":0,"ph":"X","args":{},"dur":47.120000004768372,"name":"AdjustCompileFlags","tid":250166,"ts":276704866.97100002},{"pid":0,"ph":"i","args":{"Message":"ASTWorker building file /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp version 2 with command clangd fallback\n[/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils]\n/usr/lib/llvm-14/bin/clang -I/home/<USER>/Desktop/ysyx-workbench/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/nemu/src/isa/riscv32/include/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/build/obj_dir/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/klib/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/src/platform/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include -I/home/<USER>/Desktop/ysyx-workbench/nvboard/include/ -I/usr/include/SDL2/ -I/usr/share/verilator/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/csrc/include -include=/headers/file.h -I/other/headers -resource-dir=/home/<USER>/.config/Code/User/globalStorage/llvm-vs-code-extensions.vscode-clangd/install/19.1.2/clangd_19.1.2/lib/clang/19 -- /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"name":"Log","tid":250166,"ts":276705130.29400003},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250167,"ts":276706355.26200002},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":1336.0500000119209,"name":"CreatePreamblePatch","tid":250166,"ts":276706354.02899998},{"pid":0,"ph":"X","args":{},"dur":9.4180000424385071,"name":"ClangTidyOpts","tid":250166,"ts":276707737.89099997},{"pid":0,"ph":"X","args":{},"dur":983.69800001382828,"name":"ClangTidyInit","tid":250166,"ts":276708301.486},{"pid":0,"ph":"X","args":{},"dur":208.70800000429153,"name":"ClangTidyMatch","tid":250166,"ts":276710799.35399997},{"pid":0,"ph":"X","args":{},"dur":1.6829999685287476,"name":"IncludeCleaner::getUnused","tid":250166,"ts":276711248.64200002},{"pid":0,"ph":"X","args":{},"dur":118.54699999094009,"name":"include_cleaner::walkUsed","tid":250166,"ts":276711138.57099998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(14)"},"name":"Log","tid":241390,"ts":276711456.04699999},{"pid":0,"ph":"X","args":{},"dur":224.47799998521805,"name":"IncludeCleaner::issueIncludeCleanerDiagnostics","tid":250166,"ts":276711262.85900003},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":5274.5080000162125,"name":"BuildAST","tid":250166,"ts":276706310.486},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/publishDiagnostics"},"name":"Log","tid":250166,"ts":276711831.07300001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/inactiveRegions"},"name":"Log","tid":250166,"ts":276711870.66900003},{"pid":0,"ph":"X","args":{},"dur":60.716000020503998,"name":"ASTSignals::derive","tid":250166,"ts":276711889.875},{"pid":0,"ph":"X","args":{},"dur":342.96499997377396,"name":"Running main AST callback","tid":250166,"ts":276711613.15700001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":10.469999969005585,"name":"getConfig","tid":250166,"ts":276711976.12},{"pid":0,"ph":"X","args":{},"dur":41.770000040531158,"name":"Build AST","tid":250166,"ts":276711972.91399997},{"pid":0,"ph":"X","args":{},"dur":7173.2709999680519,"name":"Update","tid":250166,"ts":276704795.85600001},{"pid":0,"ph":"X","args":{"Params":{"contentChanges":[{"range":{"end":{"character":2,"line":27},"start":{"character":2,"line":27}},"rangeLength":0,"text":"// "}],"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp","version":2}}},"dur":68.220000028610229,"name":"textDocument/didChange","tid":241390,"ts":276654637.20099998},{"pid":0,"ph":"X","args":{"CurrentRequest":"Update","PreambleRequestsNames":["Build AST"],"RequestsNames":[]},"dur":2.6050000190734863,"name":"Queued:InlayHints","tid":241390,"ts":276711498.78899997},{"pid":0,"ph":"s","cat":"mock_cat","id":17,"name":"Context crosses threads","tid":241390,"ts":276711420.66000003},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":17,"name":"Context crosses threads","tid":250166,"ts":276712046.384},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":3.3260000348091125,"name":"getConfig","tid":250166,"ts":276712048.83899999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(14) 0 ms"},"name":"Log","tid":250166,"ts":276712101.21899998},{"pid":0,"ph":"X","args":{},"dur":87.486999988555908,"name":"InlayHints","tid":250166,"ts":276712041.89600003},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":0,"line":37},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":23}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":31,"line":23}}]},"dur":83.037999987602234,"name":"textDocument/inlayHint","tid":241390,"ts":276711420.66000003},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":276712151.23400003},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(15)"},"name":"Log","tid":241390,"ts":276860226.79500002},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250216},{"pid":0,"ph":"X","args":{},"dur":10.629999995231628,"name":"WaitForFreeSemaphoreSlot","tid":250216,"ts":276860333.45899999},{"pid":0,"ph":"s","cat":"mock_cat","id":18,"name":"Context crosses threads","tid":241390,"ts":276860206.61699998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":18,"name":"Context crosses threads","tid":250216,"ts":276860399.986},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":20.268999993801117,"name":"getConfig","tid":250216,"ts":276860392.22100002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(15) 0 ms"},"name":"Log","tid":250216,"ts":276860553.17799997},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":98.418000042438507,"name":"textDocument/foldingRange","tid":241390,"ts":276860206.61699998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(16)"},"name":"Log","tid":241390,"ts":276910954.69599998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.9140000343322754,"name":"Queued:codeAction","tid":241390,"ts":276910997.72799999},{"pid":0,"ph":"s","cat":"mock_cat","id":19,"name":"Context crosses threads","tid":241390,"ts":276910932.273},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":19,"name":"Context crosses threads","tid":250166,"ts":276911055.92900002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":9.2179999947547913,"name":"getConfig","tid":250166,"ts":276911060.19700003},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(16) 0 ms"},"name":"Log","tid":250166,"ts":276911109.662},{"pid":0,"ph":"X","args":{},"dur":89.069999992847443,"name":"codeAction","tid":250166,"ts":276911049.32700002},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":5,"line":27},"start":{"character":5,"line":27}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[]},"dur":71.887000024318695,"name":"textDocument/codeAction","tid":241390,"ts":276910932.273},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":276911174.09500003},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(17)"},"name":"Log","tid":241390,"ts":276954212.30900002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.812999963760376,"name":"Queued:SemanticHighlights","tid":241390,"ts":276954251.18300003},{"pid":0,"ph":"s","cat":"mock_cat","id":20,"name":"Context crosses threads","tid":241390,"ts":276954200.77700001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":20,"name":"Context crosses threads","tid":250166,"ts":276954312.72000003},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":10.190000057220459,"name":"getConfig","tid":250166,"ts":276954316.86799997},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(17) 0 ms"},"name":"Log","tid":250166,"ts":276954437.78899997},{"pid":0,"ph":"X","args":{},"dur":165.43600004911423,"name":"SemanticHighlights","tid":250166,"ts":276954306.48799998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":{"data":[17,7,8,18,66048,0,9,15,0,131073,1,0,4,8,66048,0,6,6,0,131075,0,9,4,19,131072,2,5,8,3,131075,0,21,8,2,16403,1,2,6,0,131072,0,7,1,21,0,0,2,6,19,131072,1,6,8,2,16400,0,9,2,21,0,0,3,4,19,131072,1,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,6,8,2,16400,1,4,6,19,131072,0,7,2,1,16384,0,25,8,2,16400,1,4,6,0,131072,0,7,1,21,0,0,2,2,1,16384,5,5,10,3,131075,1,7,17,19,131072,3,11,6,19,131072,0,7,12,19,131072,0,15,15,0,131072,0,16,2,21,0,0,3,16,19,131072],"resultId":"3"}},"dur":56.287000000476837,"name":"textDocument/semanticTokens/full","tid":241390,"ts":276954200.77700001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":276954531.778},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(18)"},"name":"Log","tid":241390,"ts":277011266.81400001},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250217},{"pid":0,"ph":"X","args":{},"dur":10.629999995231628,"name":"WaitForFreeSemaphoreSlot","tid":250217,"ts":277011383.25700003},{"pid":0,"ph":"s","cat":"mock_cat","id":21,"name":"Context crosses threads","tid":241390,"ts":277011256.15399998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":21,"name":"Context crosses threads","tid":250217,"ts":277011407.23199999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":13.476000010967255,"name":"getConfig","tid":250217,"ts":277011402.72399998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(18) 0 ms"},"name":"Log","tid":250217,"ts":277011544.204},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":99.961000025272369,"name":"textDocument/foldingRange","tid":241390,"ts":277011256.15399998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentSymbol(19)"},"name":"Log","tid":241390,"ts":277274471.59500003},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.7450000047683716,"name":"Queued:DocumentSymbols","tid":241390,"ts":277274504.04699999},{"pid":0,"ph":"s","cat":"mock_cat","id":22,"name":"Context crosses threads","tid":241390,"ts":277274460.30299997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":22,"name":"Context crosses threads","tid":250166,"ts":277274557.699},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":10.921000003814697,"name":"getConfig","tid":250166,"ts":277274563.5},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentSymbol(19) 0 ms"},"name":"Log","tid":250166,"ts":277274674.73199999},{"pid":0,"ph":"X","args":{},"dur":203.7389999628067,"name":"DocumentSymbols","tid":250166,"ts":277274549.18300003},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"detail":"uint64_t","kind":13,"name":"g_nr_guest_inst","range":{"end":{"character":31,"line":17},"start":{"character":0,"line":17}},"selectionRange":{"end":{"character":31,"line":17},"start":{"character":16,"line":17}}},{"detail":"FILE *","kind":13,"name":"log_fp","range":{"end":{"character":19,"line":18},"start":{"character":0,"line":18}},"selectionRange":{"end":{"character":12,"line":18},"start":{"character":6,"line":18}}},{"detail":"void (const char *)","kind":12,"name":"init_log","range":{"end":{"character":1,"line":28},"start":{"character":0,"line":20}},"selectionRange":{"end":{"character":13,"line":20},"start":{"character":5,"line":20}}},{"detail":"bool ()","kind":12,"name":"log_enable","range":{"end":{"character":1,"line":36},"start":{"character":0,"line":30}},"selectionRange":{"end":{"character":15,"line":30},"start":{"character":5,"line":30}}}]},"dur":52.470000028610229,"name":"textDocument/documentSymbol","tid":241390,"ts":277274460.30299997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":277274815.00999999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(20)"},"name":"Log","tid":241390,"ts":277460564.58600003},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.0440000295639038,"name":"Queued:InlayHints","tid":241390,"ts":277460615.28299999},{"pid":0,"ph":"s","cat":"mock_cat","id":23,"name":"Context crosses threads","tid":241390,"ts":277460549.86799997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":23,"name":"Context crosses threads","tid":250166,"ts":277460656.02100003},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":10.870999991893768,"name":"getConfig","tid":250166,"ts":277460661.73199999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(20) 0 ms"},"name":"Log","tid":250166,"ts":277460756.56300002},{"pid":0,"ph":"X","args":{},"dur":159.43400001525879,"name":"InlayHints","tid":250166,"ts":277460649.04799998},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":0,"line":37},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":23}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":31,"line":23}}]},"dur":71.647000014781952,"name":"textDocument/inlayHint","tid":241390,"ts":277460549.86799997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":277460844.06},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentLink(21)"},"name":"Log","tid":241390,"ts":277660266.63300002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":5.5200000405311584,"name":"Queued:DocumentLinks","tid":241390,"ts":277660339.25199997},{"pid":0,"ph":"s","cat":"mock_cat","id":24,"name":"Context crosses threads","tid":241390,"ts":277660237.73799998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":24,"name":"Context crosses threads","tid":250166,"ts":277660399.597},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":21.090000033378601,"name":"getConfig","tid":250166,"ts":277660406.81099999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentLink(21) 0 ms"},"name":"Log","tid":250166,"ts":277660484.699},{"pid":0,"ph":"X","args":{},"dur":151.4990000128746,"name":"DocumentLinks","tid":250166,"ts":277660388.185},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"range":{"end":{"character":19,"line":15},"start":{"character":9,"line":15}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/common.h"}]},"dur":116.46200001239777,"name":"textDocument/documentLink","tid":241390,"ts":277660237.73799998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":277660570.78399998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/didChange"},"name":"Log","tid":241390,"ts":277664042.89099997},{"pid":0,"ph":"s","cat":"mock_cat","id":25,"name":"Context crosses threads","tid":241390,"ts":277664025.66799998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":25,"name":"Context crosses threads","tid":250166,"ts":277664114.417},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":277664128.66500002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/didSave"},"name":"Log","tid":241390,"ts":277674332.88099998},{"pid":0,"ph":"i","args":{"Message":"File version went from 3 to 3"},"name":"Log","tid":241390,"ts":277674377.08499998},{"pid":0,"ph":"X","args":{"next_request":"Update","sleep_ms":49},"dur":10354.483000040054,"name":"Debounce","tid":250166,"ts":277664102.52499998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.5850000381469727,"name":"Queued:Update","tid":241390,"ts":277664087.32599998},{"pid":0,"ph":"X","args":{"Params":{"contentChanges":[{"range":{"end":{"character":6,"line":33},"start":{"character":5,"line":33}},"rangeLength":1,"text":""}],"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp","version":3}}},"dur":70.254000008106232,"name":"textDocument/didChange","tid":241390,"ts":277664025.66799998},{"pid":0,"ph":"s","cat":"mock_cat","id":26,"name":"Context crosses threads","tid":241390,"ts":277674319.16500002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":26,"name":"Context crosses threads","tid":250166,"ts":277674499.49900001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":277674513.72600001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(22)"},"name":"Log","tid":241390,"ts":277715144.89600003},{"pid":0,"ph":"X","args":{"next_request":"Update","sleep_ms":49},"dur":40716.81299996376,"name":"Debounce","tid":250166,"ts":277674493.62800002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":["Update"]},"dur":3.3170000314712524,"name":"Queued:Update","tid":241390,"ts":277674439.46399999},{"pid":0,"ph":"s","cat":"mock_cat","id":27,"name":"Context crosses threads","tid":241390,"ts":277674319.16500002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":27,"name":"Context crosses threads","tid":250166,"ts":277715240.53799999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":10.760999977588654,"name":"getConfig","tid":250166,"ts":277715244.736},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":7.2639999985694885,"name":"getConfig","tid":250166,"ts":277715265.49599999},{"pid":0,"ph":"i","args":{"Message":"Failed to find compilation database for /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"name":"Log","tid":250166,"ts":277715287.53799999},{"pid":0,"ph":"X","args":{},"dur":52.670000016689301,"name":"AdjustCompileFlags","tid":250166,"ts":277715306.37400001},{"pid":0,"ph":"i","args":{"Message":"ASTWorker building file /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp version 3 with command clangd fallback\n[/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils]\n/usr/lib/llvm-14/bin/clang -I/home/<USER>/Desktop/ysyx-workbench/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/nemu/src/isa/riscv32/include/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/build/obj_dir/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/klib/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/src/platform/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include -I/home/<USER>/Desktop/ysyx-workbench/nvboard/include/ -I/usr/include/SDL2/ -I/usr/share/verilator/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/csrc/include -include=/headers/file.h -I/other/headers -resource-dir=/home/<USER>/.config/Code/User/globalStorage/llvm-vs-code-extensions.vscode-clangd/install/19.1.2/clangd_19.1.2/lib/clang/19 -- /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"name":"Log","tid":250166,"ts":277715625.45300001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250167,"ts":277716713.56},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":1240.4880000352859,"name":"CreatePreamblePatch","tid":250166,"ts":277716716.64499998},{"pid":0,"ph":"X","args":{},"dur":11.300999999046326,"name":"ClangTidyOpts","tid":250166,"ts":277718040.30199999},{"pid":0,"ph":"X","args":{},"dur":984.77999997138977,"name":"ClangTidyInit","tid":250166,"ts":277718457.92900002},{"pid":0,"ph":"X","args":{},"dur":188.30900001525879,"name":"ClangTidyMatch","tid":250166,"ts":277720842.75099999},{"pid":0,"ph":"X","args":{},"dur":1.2619999647140503,"name":"IncludeCleaner::getUnused","tid":250166,"ts":277721200.50400001},{"pid":0,"ph":"X","args":{},"dur":84.97299998998642,"name":"include_cleaner::walkUsed","tid":250166,"ts":277721123.35600001},{"pid":0,"ph":"X","args":{},"dur":206.71399998664856,"name":"IncludeCleaner::issueIncludeCleanerDiagnostics","tid":250166,"ts":277721214.19},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":4803.4889999628067,"name":"BuildAST","tid":250166,"ts":277716678.38200003},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/publishDiagnostics"},"name":"Log","tid":250166,"ts":277721736.236},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/inactiveRegions"},"name":"Log","tid":250166,"ts":277721778.20599997},{"pid":0,"ph":"X","args":{},"dur":57.129000008106232,"name":"ASTSignals::derive","tid":250166,"ts":277721797.91399997},{"pid":0,"ph":"X","args":{},"dur":351.10000002384186,"name":"Running main AST callback","tid":250166,"ts":277721509.41299999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":10.100000023841858,"name":"getConfig","tid":250166,"ts":277721875.171},{"pid":0,"ph":"X","args":{},"dur":37.87200003862381,"name":"Build AST","tid":250166,"ts":277721873.28799999},{"pid":0,"ph":"X","args":{},"dur":6634.8539999723434,"name":"Update","tid":250166,"ts":277715234.94700003},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}}},"dur":130.81900000572205,"name":"textDocument/didSave","tid":241390,"ts":277674319.16500002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":["Update"]},"dur":2.3640000224113464,"name":"Queued:InlayHints","tid":241390,"ts":277715186.926},{"pid":0,"ph":"s","cat":"mock_cat","id":28,"name":"Context crosses threads","tid":241390,"ts":277715126.42000002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":28,"name":"Context crosses threads","tid":250166,"ts":277721941.32800001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":4.8189999461174011,"name":"getConfig","tid":250166,"ts":277721945.03500003},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(22) 6 ms"},"name":"Log","tid":250166,"ts":277722029.366},{"pid":0,"ph":"X","args":{},"dur":137.60199999809265,"name":"InlayHints","tid":250166,"ts":277721936.59899998},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":0,"line":37},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":23}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":31,"line":23}}]},"dur":66.937999963760376,"name":"textDocument/inlayHint","tid":241390,"ts":277715126.42000002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":277722119.88800001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(23)"},"name":"Log","tid":241390,"ts":277881987.736},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250238},{"pid":0,"ph":"X","args":{},"dur":12.745000004768372,"name":"WaitForFreeSemaphoreSlot","tid":250238,"ts":277882103.12599999},{"pid":0,"ph":"s","cat":"mock_cat","id":29,"name":"Context crosses threads","tid":241390,"ts":277881973.15799999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":29,"name":"Context crosses threads","tid":250238,"ts":277882184.57200003},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":71.976999998092651,"name":"getConfig","tid":250238,"ts":277882126.90200001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(23) 0 ms"},"name":"Log","tid":250238,"ts":277882341.63200003},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":110.03000003099442,"name":"textDocument/foldingRange","tid":241390,"ts":277881973.15799999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(24)"},"name":"Log","tid":241390,"ts":277964067.60600001},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.187000036239624,"name":"Queued:SemanticHighlights","tid":241390,"ts":277964113.83399999},{"pid":0,"ph":"s","cat":"mock_cat","id":30,"name":"Context crosses threads","tid":241390,"ts":277964051.27499998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":30,"name":"Context crosses threads","tid":250166,"ts":277964167.39700001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":11.452000021934509,"name":"getConfig","tid":250166,"ts":277964171.815},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(24) 0 ms"},"name":"Log","tid":250166,"ts":277964293.55699998},{"pid":0,"ph":"X","args":{},"dur":173.40099996328354,"name":"SemanticHighlights","tid":250166,"ts":277964160.07300001},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":{"data":[17,7,8,18,66048,0,9,15,0,131073,1,0,4,8,66048,0,6,6,0,131075,0,9,4,19,131072,2,5,8,3,131075,0,21,8,2,16403,1,2,6,0,131072,0,7,1,21,0,0,2,6,19,131072,1,6,8,2,16400,0,9,2,21,0,0,3,4,19,131072,1,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,6,8,2,16400,1,4,6,19,131072,0,7,2,1,16384,0,25,8,2,16400,1,4,6,0,131072,0,7,1,21,0,0,2,2,1,16384,5,5,10,3,131075,1,7,17,19,131072,3,11,6,19,131072,0,7,12,19,131072,0,15,15,0,131072,0,16,2,21,0,0,3,16,19,131072],"resultId":"4"}},"dur":70.263999998569489,"name":"textDocument/semanticTokens/full","tid":241390,"ts":277964051.27499998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":277964359.87400001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentSymbol(25)"},"name":"Log","tid":241390,"ts":278014052.80900002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.906000018119812,"name":"Queued:DocumentSymbols","tid":241390,"ts":278014095.20999998},{"pid":0,"ph":"s","cat":"mock_cat","id":31,"name":"Context crosses threads","tid":241390,"ts":278014039.41399997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":31,"name":"Context crosses threads","tid":250166,"ts":278014144.73500001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":9.4979999661445618,"name":"getConfig","tid":250166,"ts":278014149.55400002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentSymbol(25) 0 ms"},"name":"Log","tid":250166,"ts":278014240.347},{"pid":0,"ph":"X","args":{},"dur":172.47899997234344,"name":"DocumentSymbols","tid":250166,"ts":278014138.333},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"detail":"uint64_t","kind":13,"name":"g_nr_guest_inst","range":{"end":{"character":31,"line":17},"start":{"character":0,"line":17}},"selectionRange":{"end":{"character":31,"line":17},"start":{"character":16,"line":17}}},{"detail":"FILE *","kind":13,"name":"log_fp","range":{"end":{"character":19,"line":18},"start":{"character":0,"line":18}},"selectionRange":{"end":{"character":12,"line":18},"start":{"character":6,"line":18}}},{"detail":"void (const char *)","kind":12,"name":"init_log","range":{"end":{"character":1,"line":28},"start":{"character":0,"line":20}},"selectionRange":{"end":{"character":13,"line":20},"start":{"character":5,"line":20}}},{"detail":"bool ()","kind":12,"name":"log_enable","range":{"end":{"character":1,"line":36},"start":{"character":0,"line":30}},"selectionRange":{"end":{"character":15,"line":30},"start":{"character":5,"line":30}}}]},"dur":62.760000050067902,"name":"textDocument/documentSymbol","tid":241390,"ts":278014039.41399997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":278014375.85600001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(26)"},"name":"Log","tid":241390,"ts":278085403.01999998},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250239},{"pid":0,"ph":"X","args":{},"dur":10.629999995231628,"name":"WaitForFreeSemaphoreSlot","tid":250239,"ts":278085510.69599998},{"pid":0,"ph":"s","cat":"mock_cat","id":32,"name":"Context crosses threads","tid":241390,"ts":278085387.801},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":32,"name":"Context crosses threads","tid":250239,"ts":278085535.523},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":15.208999991416931,"name":"getConfig","tid":250239,"ts":278085530.273},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(26) 0 ms"},"name":"Log","tid":250239,"ts":278085649.51099998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":104.86000001430511,"name":"textDocument/foldingRange","tid":241390,"ts":278085387.801},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(27)"},"name":"Log","tid":241390,"ts":278148228.13},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.828000009059906,"name":"Queued:codeAction","tid":241390,"ts":278148280.14899999},{"pid":0,"ph":"s","cat":"mock_cat","id":33,"name":"Context crosses threads","tid":241390,"ts":278148203.14200002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":33,"name":"Context crosses threads","tid":250166,"ts":278148330.19499999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":12.402999997138977,"name":"getConfig","tid":250166,"ts":278148337.01800001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(27) 0 ms"},"name":"Log","tid":250166,"ts":278148409.98699999},{"pid":0,"ph":"X","args":{},"dur":125.00799995660782,"name":"codeAction","tid":250166,"ts":278148320.27600002},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":5,"line":27},"start":{"character":5,"line":27}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[]},"dur":89.060000002384186,"name":"textDocument/codeAction","tid":241390,"ts":278148203.14200002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":278148477.49599999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(28)"},"name":"Log","tid":241390,"ts":278235490.75199997},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250245},{"pid":0,"ph":"X","args":{},"dur":13.546000003814697,"name":"WaitForFreeSemaphoreSlot","tid":250245,"ts":278235584.90100002},{"pid":0,"ph":"s","cat":"mock_cat","id":34,"name":"Context crosses threads","tid":241390,"ts":278235475.48299998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":34,"name":"Context crosses threads","tid":250245,"ts":278235618.22500002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":21.620999991893768,"name":"getConfig","tid":250245,"ts":278235610.19999999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(28) 0 ms"},"name":"Log","tid":250245,"ts":278235759.44499999},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":91.665000021457672,"name":"textDocument/foldingRange","tid":241390,"ts":278235475.48299998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(29)"},"name":"Log","tid":241390,"ts":278464180.58600003},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.6370000243186951,"name":"Queued:InlayHints","tid":241390,"ts":278464235.94099998},{"pid":0,"ph":"s","cat":"mock_cat","id":35,"name":"Context crosses threads","tid":241390,"ts":278464159.94599998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":35,"name":"Context crosses threads","tid":250166,"ts":278464278.56300002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":12.794999957084656,"name":"getConfig","tid":250166,"ts":278464284.80400002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(29) 0 ms"},"name":"Log","tid":250166,"ts":278464371.79100001},{"pid":0,"ph":"X","args":{},"dur":138.14399999380112,"name":"InlayHints","tid":250166,"ts":278464271.00800002},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":0,"line":37},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":23}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":31,"line":23}}]},"dur":87.136000037193298,"name":"textDocument/inlayHint","tid":241390,"ts":278464159.94599998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":278464436.59399998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentLink(30)"},"name":"Log","tid":241390,"ts":278663922.43800002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.7070000171661377,"name":"Queued:DocumentLinks","tid":241390,"ts":278663985.699},{"pid":0,"ph":"s","cat":"mock_cat","id":36,"name":"Context crosses threads","tid":241390,"ts":278663896.79900002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":36,"name":"Context crosses threads","tid":250166,"ts":278664034.66299999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":14.977999985218048,"name":"getConfig","tid":250166,"ts":278664044.27100003},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentLink(30) 0 ms"},"name":"Log","tid":250166,"ts":278664089.27700001},{"pid":0,"ph":"X","args":{},"dur":133.6149999499321,"name":"DocumentLinks","tid":250166,"ts":278664024.73400003},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"range":{"end":{"character":19,"line":15},"start":{"character":9,"line":15}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/common.h"}]},"dur":97.415999948978424,"name":"textDocument/documentLink","tid":241390,"ts":278663896.79900002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":278664179.54900002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(31)"},"name":"Log","tid":241390,"ts":278830209.35799998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.5350000262260437,"name":"Queued:SemanticHighlights","tid":241390,"ts":278830248.352},{"pid":0,"ph":"s","cat":"mock_cat","id":37,"name":"Context crosses threads","tid":241390,"ts":278830196.02200001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":37,"name":"Context crosses threads","tid":250166,"ts":278830287.116},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":11.321000039577484,"name":"getConfig","tid":250166,"ts":278830291.10399997},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(31) 0 ms"},"name":"Log","tid":250166,"ts":278830414.45899999},{"pid":0,"ph":"X","args":{},"dur":177.36799997091293,"name":"SemanticHighlights","tid":250166,"ts":278830280.01300001},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":{"data":[17,7,8,18,66048,0,9,15,0,131073,1,0,4,8,66048,0,6,6,0,131075,0,9,4,19,131072,2,5,8,3,131075,0,21,8,2,16403,1,2,6,0,131072,0,7,1,21,0,0,2,6,19,131072,1,6,8,2,16400,0,9,2,21,0,0,3,4,19,131072,1,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,6,8,2,16400,1,4,6,19,131072,0,7,2,1,16384,0,25,8,2,16400,1,4,6,0,131072,0,7,1,21,0,0,2,2,1,16384,5,5,10,3,131075,1,7,17,19,131072,3,11,6,19,131072,0,7,12,19,131072,0,15,15,0,131072,0,16,2,21,0,0,3,16,19,131072],"resultId":"5"}},"dur":59.324000000953674,"name":"textDocument/semanticTokens/full","tid":241390,"ts":278830196.02200001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":278830486.43699998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(32)"},"name":"Log","tid":241390,"ts":278904739.77899998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.1640000343322754,"name":"Queued:InlayHints","tid":241390,"ts":278904775.98799998},{"pid":0,"ph":"s","cat":"mock_cat","id":38,"name":"Context crosses threads","tid":241390,"ts":278904723.79799998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":38,"name":"Context crosses threads","tid":250166,"ts":278904815.80400002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":12.834999978542328,"name":"getConfig","tid":250166,"ts":278904820.69300002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(32) 0 ms"},"name":"Log","tid":250166,"ts":278904923.69},{"pid":0,"ph":"X","args":{},"dur":160.44600003957748,"name":"InlayHints","tid":250166,"ts":278904808.39999998},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":0,"line":37},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":23}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":31,"line":23}}]},"dur":60.375,"name":"textDocument/inlayHint","tid":241390,"ts":278904723.79799998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":278905033.69},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(33)"},"name":"Log","tid":241390,"ts":279031698.87599999},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250252},{"pid":0,"ph":"X","args":{},"dur":11.48199999332428,"name":"WaitForFreeSemaphoreSlot","tid":250252,"ts":279031822.61199999},{"pid":0,"ph":"s","cat":"mock_cat","id":39,"name":"Context crosses threads","tid":241390,"ts":279031681.94400001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":39,"name":"Context crosses threads","tid":250252,"ts":279031849.01300001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":16.391000032424927,"name":"getConfig","tid":250252,"ts":279031843.61299998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(33) 0 ms"},"name":"Log","tid":250252,"ts":279031969.34299999},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":110.12000000476837,"name":"textDocument/foldingRange","tid":241390,"ts":279031681.94400001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(34)"},"name":"Log","tid":241390,"ts":279083931.449},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.0839999914169312,"name":"Queued:codeAction","tid":241390,"ts":279083973.93000001},{"pid":0,"ph":"s","cat":"mock_cat","id":40,"name":"Context crosses threads","tid":241390,"ts":279083913.25400001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":40,"name":"Context crosses threads","tid":250166,"ts":279084029.83700001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":11.071000039577484,"name":"getConfig","tid":250166,"ts":279084034.51599997},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(34) 0 ms"},"name":"Log","tid":250166,"ts":279084091.935},{"pid":0,"ph":"X","args":{},"dur":95.000999987125397,"name":"codeAction","tid":250166,"ts":279084022.96399999},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":5,"line":27},"start":{"character":5,"line":27}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[]},"dur":67.969999969005585,"name":"textDocument/codeAction","tid":241390,"ts":279083913.25400001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":279084142.15100002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(35)"},"name":"Log","tid":241390,"ts":279181954.51800001},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250253},{"pid":0,"ph":"X","args":{},"dur":10.410000026226044,"name":"WaitForFreeSemaphoreSlot","tid":250253,"ts":279182064.338},{"pid":0,"ph":"s","cat":"mock_cat","id":41,"name":"Context crosses threads","tid":241390,"ts":279181942.72600001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":41,"name":"Context crosses threads","tid":250253,"ts":279182088.55400002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":14.987999975681305,"name":"getConfig","tid":250253,"ts":279182083.495},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(35) 0 ms"},"name":"Log","tid":250253,"ts":279182218.45200002},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":103.91799998283386,"name":"textDocument/foldingRange","tid":241390,"ts":279181942.72600001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/didOpen"},"name":"Log","tid":241390,"ts":291579376.74699998},{"pid":0,"ph":"X","args":{},"dur":57.939999997615814,"name":"AdjustCompileFlags","tid":241390,"ts":291579432.12300003},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.2760000228881836,"name":"Queued:Update","tid":241390,"ts":291579576.208},{"pid":0,"ph":"M","args":{"name":"ker:monitor.cpp"},"name":"thread_name","tid":250490},{"pid":0,"ph":"s","cat":"mock_cat","id":42,"name":"Context crosses threads","tid":241390,"ts":291579361.167},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":42,"name":"Context crosses threads","tid":250490,"ts":291579623.86900002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":158.84400004148483,"name":"getConfig","tid":250490,"ts":291579628.36699998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":4.0679999589920044,"name":"getConfig","tid":250490,"ts":291579794.98500001},{"pid":0,"ph":"i","args":{"Message":"Failed to find compilation database for /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"name":"Log","tid":250490,"ts":291579829.662},{"pid":0,"ph":"X","args":{},"dur":37.101000010967255,"name":"AdjustCompileFlags","tid":250490,"ts":291579847.32499999},{"pid":0,"ph":"i","args":{"Message":"ASTWorker building file /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp version 1 with command clangd fallback\n[/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor]\n/usr/lib/llvm-14/bin/clang -I/home/<USER>/Desktop/ysyx-workbench/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/nemu/src/isa/riscv32/include/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/build/obj_dir/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/klib/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/src/platform/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include -I/home/<USER>/Desktop/ysyx-workbench/nvboard/include/ -I/usr/include/SDL2/ -I/usr/share/verilator/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/csrc/include -include=/headers/file.h -I/other/headers -resource-dir=/home/<USER>/.config/Code/User/globalStorage/llvm-vs-code-extensions.vscode-clangd/install/19.1.2/clangd_19.1.2/lib/clang/19 -- /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"name":"Log","tid":250490,"ts":291579901.54900002},{"pid":0,"ph":"M","args":{"name":"ker:monitor.cpp"},"name":"thread_name","tid":250491},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250491,"ts":291580985.01700002},{"pid":0,"ph":"s","cat":"mock_cat","id":43,"name":"Context crosses threads","tid":250490,"ts":291579614.06},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":43,"name":"Context crosses threads","tid":250491,"ts":291581050.42199999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(36)"},"name":"Log","tid":241390,"ts":291588404.68800002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentSymbol(37)"},"name":"Log","tid":241390,"ts":291588842.36400002},{"pid":0,"ph":"i","args":{"Message":"Built preamble of size 568744 for file /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp version 1 in 0.04 seconds"},"name":"Log","tid":250491,"ts":291617942.34399998},{"pid":0,"ph":"X","args":{},"dur":148.84399998188019,"name":"Running PreambleCallback","tid":250491,"ts":291618027.958},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":37147.931999981403,"name":"BuildPreamble","tid":250491,"ts":291581040.833},{"pid":0,"ph":"i","args":{"Message":"--> workspace/semanticTokens/refresh(1)"},"name":"Log","tid":250491,"ts":291618231.59600002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":21.962000012397766,"name":"getConfig","tid":250490,"ts":291618275.44999999},{"pid":0,"ph":"M","args":{"name":"or/monitor.cpp1"},"name":"thread_name","tid":250492},{"pid":0,"ph":"i","args":{"Message":"<-- reply(1)"},"name":"Log","tid":241390,"ts":291619042.78500003},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":2633.5560000538826,"name":"CreatePreamblePatch","tid":250490,"ts":291618394.86799997},{"pid":0,"ph":"X","args":{},"dur":168.86300003528595,"name":"ClangTidyOpts","tid":250490,"ts":291621104.89999998},{"pid":0,"ph":"X","args":{},"dur":1534.1580000519753,"name":"ClangTidyInit","tid":250490,"ts":291621834.00199997},{"pid":0,"ph":"X","args":{"results":0},"dur":6.2120000123977661,"name":"MemIndex fuzzyFind","tid":250490,"ts":291629623.94999999},{"pid":0,"ph":"X","args":{"query":"(LIMIT 10000 (& T=cpu S= ?=Restricted For Code Completion))"},"dur":38.492999970912933,"name":"Dex fuzzyFind","tid":250490,"ts":291629650.912},{"pid":0,"ph":"X","args":{"dynamic":0,"merged":0,"static":1,"static_dropped":0},"dur":85.763000011444092,"name":"MergedIndex fuzzyFind","tid":250490,"ts":291629616.79699999},{"pid":0,"ph":"X","args":{"results":0},"dur":1.8040000200271606,"name":"MemIndex fuzzyFind","tid":250490,"ts":291629713.00999999},{"pid":0,"ph":"X","args":{},"dur":1.6629999876022339,"name":"ProjectAwareIndex::fuzzyFind","tid":250490,"ts":291629721.116},{"pid":0,"ph":"X","args":{"dynamic":0,"merged":0,"static":0,"static_dropped":0},"dur":16.89300000667572,"name":"MergedIndex fuzzyFind","tid":250490,"ts":291629711.45700002},{"pid":0,"ph":"X","args":{"dynamic":1,"merged":0,"static":0,"static_dropped":0},"dur":122.46399998664856,"name":"MergedIndex fuzzyFind","tid":250490,"ts":291629613.30000001},{"pid":0,"ph":"X","args":{},"dur":1779.0250000357628,"name":"ClangTidyMatch","tid":250490,"ts":291631080.08999997},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":291632922.676},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":291632955.37900001},{"pid":0,"ph":"X","args":{},"dur":3.6270000338554382,"name":"IncludeCleaner::getUnused","tid":250490,"ts":291633593.61699998},{"pid":0,"ph":"X","args":{},"dur":452.57400000095367,"name":"include_cleaner::walkUsed","tid":250490,"ts":291633154.46899998},{"pid":0,"ph":"X","args":{},"dur":497.73000001907349,"name":"IncludeCleaner::issueIncludeCleanerDiagnostics","tid":250490,"ts":291633617.23199999},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":15888.93599998951,"name":"BuildAST","tid":250490,"ts":291618322.94099998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/publishDiagnostics"},"name":"Log","tid":250490,"ts":291635128.90700001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/inactiveRegions"},"name":"Log","tid":250490,"ts":291635195.505},{"pid":0,"ph":"X","args":{},"dur":311.98600000143051,"name":"ASTSignals::derive","tid":250490,"ts":291635220.40200001},{"pid":0,"ph":"X","args":{},"dur":1293.4280000329018,"name":"Running main AST callback","tid":250490,"ts":291634247.27499998},{"pid":0,"ph":"X","args":{},"dur":17317.743999958038,"name":"Build AST","tid":250490,"ts":291618269.86900002},{"pid":0,"ph":"X","args":{},"dur":38648.225000023842,"name":"Update","tid":250490,"ts":291579614.06},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"languageId":"cpp","text":"#include <common.h>\n#include <cstdint>\n#include <cstdio>\n#include <memory/paddr.h>\n#include <getopt.h>\n#include <cpu/cpu.h>\n#include <cpu/difftest.h>\n\nvoid init_sdb();\nvoid init_rand();\nvoid init_log(const char *log_file);\nvoid init_mem();\nvoid init_difftest(char *ref_so_file, long img_size, int port);\n// void init_disasm(const char *triple);\n\n\nvoid welcome () {\n  Log(\"Trace: %s\", MUXDEF(CONFIG_TRACE, ANSI_FMT(\"ON\", ANSI_FG_GREEN), ANSI_FMT(\"OFF\", ANSI_FG_RED)));\n  IFDEF(CONFIG_TRACE, Log(\"If trace is enabled, a log file will be generated \"\n        \"to record the trace. This may lead to a large log file. \"\n        \"If it is not necessary, you can disable it in menuconfig\"));\n  Log(\"Build time: %s, %s\", __TIME__, __DATE__);\n  printf(\"Welcome to YSYX-SOC!\\n\");\n  // printf(\"For help, type \\\"help\\\"\\n\");\n  assert(1);\n}\n\n#include <getopt.h>\n\nvoid sdb_set_batch_mode();\n\nstatic char *log_file = NULL;\nstatic char *diff_so_file = NULL;\nstatic char *img_file = NULL;\nFILE *elf_file = NULL;\nstatic int difftest_port = 1234;\n\n\nextern void ftrace_init();\nstatic int parse_args(int argc, char *argv[]) {\n    const struct option table[] = {\n        {\"batch\"    , no_argument      , NULL, 'b'},\n        {\"diff\"     , required_argument, NULL, 'd'},\n        {\"port\"     , required_argument, NULL, 'p'},\n        {\"log\"      , required_argument, NULL, 'l'},\n        {\"elf\"      , required_argument, NULL, 'e'}, // 参数是带有elf文件路径的字符串\n        {0          , 0                , NULL,  0 },\n    };\n    int o;\n    while ( (o = getopt_long(argc, argv, \"-bhl:d:p:e:\", table, NULL)) != -1) {\n      switch (o) {\n        case 'b': sdb_set_batch_mode(); break;\n        case 'l': log_file = optarg; break;\n        case 'p': sscanf(optarg, \"%d\", &difftest_port); break;\n        case 'd': diff_so_file = optarg; break;\n        case 'e': elf_file = fopen(optarg,\"rb\");ftrace_init(); break;\n        case 1: img_file = optarg; return 0;\n        default:\n          printf(\"Usage: %s [OPTION...] IMAGE [args]\\n\\n\", argv[0]);\n          printf(\"\\t-b,--batch              run with batch mode\\n\");\n          printf(\"\\t-d,--diff=REF_SO        run DiffTest with reference REF_SO\\n\");\n          printf(\"\\t-p,--port=PORT          run DiffTest with port PORT\\n\");\n          printf(\"\\t-l,--log=FILE           output log to FILE\\n\");\n          printf(\"\\t-e,--elf=ELF_FILE       Initialize ftrace \\n\");\n          printf(\"\\tIMG,IMG=IMG_FILE       Initialize img \\n\");\n          printf(\"\\n\");\n          exit(0);\n      }\n    }\n    return 0;\n\n}\n\n/* @brief : 从.bin文件中读取指令\n * @return: 所有指令大小\n */\nstatic long load_img (void) {\n    if (img_file == NULL) {\n      Log (\"No image is given. Use the default build-in image\");\n      return 4096;\n    }\n    FILE *fp = fopen (img_file, \"rb\");\n    Assert (fp, \"Can not open '%s'\", img_file);\n\n    fseek (fp, 0, SEEK_END);\n    long size = ftell (fp);\n\n    Log (\"The image is %s, size = %ld\", img_file, size);\n\n    fseek (fp, 0, SEEK_SET);\n    int ret = fread (guest_to_host (RESET_VECTOR), size, 1, fp);    // 将image的内容存入地址中\n    assert (ret == 1);\n\n    fclose (fp);\n    return size;\n}\n\nstatic const uint32_t img [] = {\n  0x800002b7,  // lui t0,0x80000\n  0x0002a023,  // sw  zero,0(t0)\n  0x0002a503,  // lw  a0,0(t0)\n  0x00100073,  // ebreak (used as nemu_trap)\n};\n\nstatic void restart() {\n  /* Set the initial program counter. */\n  cpu.pc = RESET_VECTOR;\n\n  /* The zero register is always 0. */\n  cpu.gpr[0] = 0;\n}\n\n/* @brief : 初始化指令集架构\n * @return: NONE\n */\nvoid init_isa() {\n  /* Load built-in image. */\n  memcpy(guest_to_host(RESET_VECTOR), img, sizeof(img));\n\n  /* Initialize this virtual computer system. */\n  restart();\n}\n\nvoid init_monitor (int argc, char *argv[]) {\n\n    // 分析参数\n    parse_args (argc, argv);\n    /* Set random seed. */\n    init_rand();\n\n    /* Open the log file. */\n    init_log (log_file);\n\n    // 初始化内存\n    init_mem();\n\n    init_isa();\n\n    long img_size = load_img();    // 如果没有img_file,将使用默认的img\n\n    // 如果是作为ref,则会更新img_file为dut执行的img\n    // init_difftest(diff_so_file,img_size,difftest_port);\n\n    // init_sdb();\n    // IFDEF(CONFIG_ITRACE, init_disasm(\"riscv32\"\"-pc-linux-gnu\"));\n    welcome();\n}\n","uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","version":1}}},"dur":224.25800001621246,"name":"textDocument/didOpen","tid":241390,"ts":291579361.167},{"pid":0,"ph":"X","args":{"CurrentRequest":"Update","PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.5139999985694885,"name":"Queued:codeAction","tid":241390,"ts":291588447.24000001},{"pid":0,"ph":"s","cat":"mock_cat","id":44,"name":"Context crosses threads","tid":241390,"ts":291588373.85000002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":44,"name":"Context crosses threads","tid":250490,"ts":291635673.93800002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.550000011920929,"name":"getConfig","tid":250490,"ts":291635678.90700001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(36) 47 ms"},"name":"Log","tid":250490,"ts":291635733.31099999},{"pid":0,"ph":"X","args":{},"dur":94.519999980926514,"name":"codeAction","tid":250490,"ts":291635666.79400003},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":0,"line":0},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":79.160999953746796,"name":"textDocument/codeAction","tid":241390,"ts":291588373.85000002},{"pid":0,"ph":"X","args":{"CurrentRequest":"Update","PreambleRequestsNames":[],"RequestsNames":["codeAction"]},"dur":2.3550000190734863,"name":"Queued:DocumentSymbols","tid":241390,"ts":291588863.04299998},{"pid":0,"ph":"s","cat":"mock_cat","id":45,"name":"Context crosses threads","tid":241390,"ts":291588833.57700002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":45,"name":"Context crosses threads","tid":250490,"ts":291635796.53200001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":5.199999988079071,"name":"getConfig","tid":250490,"ts":291635800.49900001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentSymbol(37) 48 ms"},"name":"Log","tid":250490,"ts":291637334.347},{"pid":0,"ph":"X","args":{},"dur":2064.6910000443459,"name":"DocumentSymbols","tid":250490,"ts":291635791.07099998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"detail":"void ()","kind":12,"name":"init_sdb","range":{"end":{"character":15,"line":8},"start":{"character":0,"line":8}},"selectionRange":{"end":{"character":13,"line":8},"start":{"character":5,"line":8}}},{"detail":"void ()","kind":12,"name":"init_rand","range":{"end":{"character":16,"line":9},"start":{"character":0,"line":9}},"selectionRange":{"end":{"character":14,"line":9},"start":{"character":5,"line":9}}},{"detail":"void (const char *)","kind":12,"name":"init_log","range":{"end":{"character":35,"line":10},"start":{"character":0,"line":10}},"selectionRange":{"end":{"character":13,"line":10},"start":{"character":5,"line":10}}},{"detail":"void ()","kind":12,"name":"init_mem","range":{"end":{"character":15,"line":11},"start":{"character":0,"line":11}},"selectionRange":{"end":{"character":13,"line":11},"start":{"character":5,"line":11}}},{"detail":"void (char *, long, int)","kind":12,"name":"init_difftest","range":{"end":{"character":62,"line":12},"start":{"character":0,"line":12}},"selectionRange":{"end":{"character":18,"line":12},"start":{"character":5,"line":12}}},{"detail":"void ()","kind":12,"name":"welcome","range":{"end":{"character":1,"line":25},"start":{"character":0,"line":16}},"selectionRange":{"end":{"character":12,"line":16},"start":{"character":5,"line":16}}},{"detail":"void ()","kind":12,"name":"sdb_set_batch_mode","range":{"end":{"character":25,"line":29},"start":{"character":0,"line":29}},"selectionRange":{"end":{"character":23,"line":29},"start":{"character":5,"line":29}}},{"detail":"char *","kind":13,"name":"log_file","range":{"end":{"character":28,"line":31},"start":{"character":0,"line":31}},"selectionRange":{"end":{"character":21,"line":31},"start":{"character":13,"line":31}}},{"detail":"char *","kind":13,"name":"diff_so_file","range":{"end":{"character":32,"line":32},"start":{"character":0,"line":32}},"selectionRange":{"end":{"character":25,"line":32},"start":{"character":13,"line":32}}},{"detail":"char *","kind":13,"name":"img_file","range":{"end":{"character":28,"line":33},"start":{"character":0,"line":33}},"selectionRange":{"end":{"character":21,"line":33},"start":{"character":13,"line":33}}},{"detail":"FILE *","kind":13,"name":"elf_file","range":{"end":{"character":21,"line":34},"start":{"character":0,"line":34}},"selectionRange":{"end":{"character":14,"line":34},"start":{"character":6,"line":34}}},{"detail":"int","kind":13,"name":"difftest_port","range":{"end":{"character":31,"line":35},"start":{"character":0,"line":35}},"selectionRange":{"end":{"character":24,"line":35},"start":{"character":11,"line":35}}},{"detail":"void ()","kind":12,"name":"ftrace_init","range":{"end":{"character":25,"line":38},"start":{"character":0,"line":38}},"selectionRange":{"end":{"character":23,"line":38},"start":{"character":12,"line":38}}},{"detail":"int (int, char **)","kind":12,"name":"parse_args","range":{"end":{"character":1,"line":71},"start":{"character":0,"line":39}},"selectionRange":{"end":{"character":21,"line":39},"start":{"character":11,"line":39}}},{"detail":"long ()","kind":12,"name":"load_img","range":{"end":{"character":1,"line":95},"start":{"character":0,"line":76}},"selectionRange":{"end":{"character":20,"line":76},"start":{"character":12,"line":76}}},{"detail":"const uint32_t[4]","kind":13,"name":"img","range":{"end":{"character":1,"line":102},"start":{"character":0,"line":97}},"selectionRange":{"end":{"character":25,"line":97},"start":{"character":22,"line":97}}},{"detail":"void ()","kind":12,"name":"restart","range":{"end":{"character":1,"line":110},"start":{"character":0,"line":104}},"selectionRange":{"end":{"character":19,"line":104},"start":{"character":12,"line":104}}},{"detail":"void ()","kind":12,"name":"init_isa","range":{"end":{"character":1,"line":121},"start":{"character":0,"line":115}},"selectionRange":{"end":{"character":13,"line":115},"start":{"character":5,"line":115}}},{"detail":"void (int, char **)","kind":12,"name":"init_monitor","range":{"end":{"character":1,"line":146},"start":{"character":0,"line":123}},"selectionRange":{"end":{"character":17,"line":123},"start":{"character":5,"line":123}}}]},"dur":33.473999977111816,"name":"textDocument/documentSymbol","tid":241390,"ts":291588833.57700002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":291638028.38200003},{"pid":0,"ph":"X","args":{},"dur":24473.421000003815,"name":"PreambleIndexing","tid":250492,"ts":291618314.815},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(38)"},"name":"Log","tid":241390,"ts":291720126.40600002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.543999969959259,"name":"Queued:SemanticHighlights","tid":241390,"ts":291720170.96100003},{"pid":0,"ph":"s","cat":"mock_cat","id":46,"name":"Context crosses threads","tid":241390,"ts":291720109.69400001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":46,"name":"Context crosses threads","tid":250490,"ts":291720266.02200001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":49.694999992847443,"name":"getConfig","tid":250490,"ts":291720272.80500001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(38) 0 ms"},"name":"Log","tid":250490,"ts":291720766.73799998},{"pid":0,"ph":"X","args":{},"dur":655.12000000476837,"name":"SemanticHighlights","tid":250490,"ts":291720207.25999999},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"data":[8,5,8,3,131073,1,5,9,3,131073,1,5,8,3,131073,0,21,8,2,16403,1,5,8,3,131073,1,5,13,3,131073,0,20,11,2,16387,0,18,8,2,16387,0,14,4,2,16387,4,5,7,3,131075,1,2,3,19,131072,1,2,5,19,131072,0,6,12,19,131072,0,14,3,19,131072,3,2,3,19,131072,1,2,6,3,131584,2,2,6,19,131072,5,5,18,3,131073,2,13,8,0,65539,0,11,4,19,131072,1,13,12,0,65539,0,15,4,19,131072,1,13,8,0,65539,0,11,4,19,131072,1,0,4,8,66048,0,6,8,0,131075,0,11,4,19,131072,1,11,13,0,65539,3,12,11,3,131073,1,11,10,3,65539,0,15,4,2,16387,0,12,4,2,16387,1,17,6,8,131584,0,7,5,1,16403,1,22,11,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,41,4,19,131072,2,8,1,1,16387,1,13,1,1,16384,0,2,1,21,0,0,2,11,3,131584,0,12,4,2,16384,0,6,4,2,16384,0,21,5,1,16400,0,7,4,19,131072,0,7,2,21,0,0,3,1,21,0,1,14,1,1,16384,1,18,18,3,131072,1,18,8,0,65536,0,9,1,21,0,0,2,6,0,131584,1,18,6,3,131584,0,7,6,0,131584,0,14,1,21,0,0,1,13,0,65536,1,18,12,0,65536,0,13,1,21,0,0,2,6,0,131584,1,18,8,0,131072,0,9,1,21,0,0,2,5,3,131584,0,6,6,0,131584,0,13,11,3,131072,1,16,8,0,65536,0,9,1,21,0,0,2,6,0,131584,2,10,6,3,131584,0,49,4,2,16384,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,4,3,131584,10,12,8,3,65539,1,8,8,0,65536,0,9,2,21,0,0,3,4,19,131072,1,6,3,19,131072,3,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,7,8,0,65536,1,4,6,19,131072,0,8,2,1,16384,0,25,8,0,65536,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,9,4,1,16387,0,7,5,3,131584,0,7,2,1,18432,2,4,3,19,131072,0,36,8,0,65536,0,10,4,1,16384,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,8,3,1,16387,0,6,5,3,131584,0,7,13,3,131072,0,15,12,19,131072,0,15,4,1,16384,0,9,2,1,18432,1,4,6,19,131072,0,8,3,1,16384,0,4,2,21,0,2,4,6,3,131584,0,8,2,1,18432,1,11,4,1,16384,3,13,8,18,66048,0,9,3,0,65555,7,12,7,3,65539,2,11,12,19,131072,9,5,8,3,131075,2,2,6,3,131584,0,7,13,3,131072,0,14,12,19,131072,0,15,3,0,65552,0,12,3,0,65552,3,2,7,3,65536,3,5,12,3,131075,0,18,4,2,16387,0,12,4,2,16387,3,4,10,3,65536,0,12,4,2,16384,0,6,4,2,18432,2,4,9,3,131072,3,4,8,3,131072,0,10,8,0,65536,3,4,8,3,131072,2,4,8,3,131072,2,9,8,1,16387,0,11,8,3,65536,7,4,7,3,131072],"resultId":"1"}},"dur":67.989000022411346,"name":"textDocument/semanticTokens/full","tid":241390,"ts":291720109.69400001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":291720925.491},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentLink(39)"},"name":"Log","tid":241390,"ts":291724314.75999999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.0640000104904175,"name":"Queued:DocumentLinks","tid":241390,"ts":291724339.96799999},{"pid":0,"ph":"s","cat":"mock_cat","id":47,"name":"Context crosses threads","tid":241390,"ts":291724303.51800001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":47,"name":"Context crosses threads","tid":250490,"ts":291724377.67000002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.300000011920929,"name":"getConfig","tid":250490,"ts":291724383.50099999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentLink(39) 0 ms"},"name":"Log","tid":250490,"ts":291724521.60399997},{"pid":0,"ph":"X","args":{},"dur":249.4559999704361,"name":"DocumentLinks","tid":250490,"ts":291724370.236},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"range":{"end":{"character":19,"line":0},"start":{"character":9,"line":0}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/common.h"},{"range":{"end":{"character":25,"line":3},"start":{"character":9,"line":3}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/memory/paddr.h"},{"range":{"end":{"character":19,"line":4},"start":{"character":9,"line":4}},"target":"file:///usr/include/getopt.h"},{"range":{"end":{"character":20,"line":5},"start":{"character":9,"line":5}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/cpu/cpu.h"},{"range":{"end":{"character":25,"line":6},"start":{"character":9,"line":6}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/cpu/difftest.h"},{"range":{"end":{"character":19,"line":27},"start":{"character":9,"line":27}},"target":"file:///usr/include/getopt.h"}]},"dur":42.060000002384186,"name":"textDocument/documentLink","tid":241390,"ts":291724303.51800001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":291724667.85399997},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(40)"},"name":"Log","tid":241390,"ts":291749836.76200002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.7169999480247498,"name":"Queued:InlayHints","tid":241390,"ts":291749886.08600003},{"pid":0,"ph":"s","cat":"mock_cat","id":48,"name":"Context crosses threads","tid":241390,"ts":291749815.39099997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":48,"name":"Context crosses threads","tid":250490,"ts":291749940.63999999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":13.154999971389771,"name":"getConfig","tid":250490,"ts":291749947.39300001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(40) 1 ms"},"name":"Log","tid":250490,"ts":291751036.75199997},{"pid":0,"ph":"X","args":{},"dur":1650.589999973774,"name":"InlayHints","tid":250490,"ts":291749931.14200002},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":17,"line":115},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":9,"line":22}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":41}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":41}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":41}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":41}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":41}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":42}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":42}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":42}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":42}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":42}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":43}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":43}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":43}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":43}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":43}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":44}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":44}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":44}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":44}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":44}},{"label":[{"value":"[4]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":45}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":45}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":45}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":45}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":45}},{"label":[{"value":"[5]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":46}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":46}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":46}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":46}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":48,"line":46}},{"kind":2,"label":[{"value":"shortopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":41,"line":49}},{"kind":2,"label":[{"value":"longopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":56,"line":49}},{"kind":2,"label":[{"value":"longind:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":63,"line":49}},{"kind":2,"label":[{"value":"s:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":25,"line":53}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":33,"line":53}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":35,"line":55}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":42,"line":55}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":58}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":59}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":60}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":61}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":62}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":63}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":64}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":65}},{"kind":2,"label":[{"value":"status:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":66}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":81}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":32,"line":81}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":84}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":84}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":84}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":23,"line":85}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":89}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":89}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":89}},{"kind":2,"label":[{"value":"ptr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":90}},{"kind":2,"label":[{"value":"paddr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":36,"line":90}},{"kind":2,"label":[{"value":"n:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":57,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":60,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":12,"line":93}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":98}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":99}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":100}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":101}}]},"dur":83.279000043869019,"name":"textDocument/inlayHint","tid":241390,"ts":291749815.39099997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":291751828.11199999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(41)"},"name":"Log","tid":241390,"ts":291796217.53500003},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250493},{"pid":0,"ph":"X","args":{},"dur":21.87200003862381,"name":"WaitForFreeSemaphoreSlot","tid":250493,"ts":291796376.58899999},{"pid":0,"ph":"s","cat":"mock_cat","id":49,"name":"Context crosses threads","tid":241390,"ts":291796189.21100003},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":49,"name":"Context crosses threads","tid":250493,"ts":291796419.20999998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":27.282000005245209,"name":"getConfig","tid":250493,"ts":291796411.21499997},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(41) 0 ms"},"name":"Log","tid":250493,"ts":291797012.86299998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endCharacter":67,"endLine":19,"kind":"region","startCharacter":8,"startLine":18},{"endCharacter":66,"endLine":19,"kind":"region","startCharacter":26,"startLine":18},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":145.13699996471405,"name":"textDocument/foldingRange","tid":241390,"ts":291796189.21100003},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(42)"},"name":"Log","tid":241390,"ts":291889205.41500002},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250497},{"pid":0,"ph":"X","args":{},"dur":15.469000041484833,"name":"WaitForFreeSemaphoreSlot","tid":250497,"ts":291889342.55699998},{"pid":0,"ph":"s","cat":"mock_cat","id":50,"name":"Context crosses threads","tid":241390,"ts":291889187.60100001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":50,"name":"Context crosses threads","tid":250497,"ts":291889373.98699999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":20.75,"name":"getConfig","tid":250497,"ts":291889367.54400003},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(42) 0 ms"},"name":"Log","tid":250497,"ts":291889841.55900002},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endCharacter":67,"endLine":19,"kind":"region","startCharacter":8,"startLine":18},{"endCharacter":66,"endLine":19,"kind":"region","startCharacter":26,"startLine":18},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":126.73199999332428,"name":"textDocument/foldingRange","tid":241390,"ts":291889187.60100001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(43)"},"name":"Log","tid":241390,"ts":292032477.50800002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.043999969959259,"name":"Queued:codeAction","tid":241390,"ts":292032517.634},{"pid":0,"ph":"s","cat":"mock_cat","id":51,"name":"Context crosses threads","tid":241390,"ts":292032453.93300003},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":51,"name":"Context crosses threads","tid":250490,"ts":292032572.52899998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":11.621999979019165,"name":"getConfig","tid":250490,"ts":292032577.01800001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(43) 0 ms"},"name":"Log","tid":250490,"ts":292032767.80199999},{"pid":0,"ph":"X","args":{},"dur":293.49000000953674,"name":"codeAction","tid":250490,"ts":292032565.08499998},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":17,"line":22},"start":{"character":10,"line":22}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":17,"line":22},"start":{"character":10,"line":22}},"tweakID":"ExtractVariable"}],"command":"clangd.applyTweak","title":"Extract subexpression to variable"},"kind":"refactor","title":"Extract subexpression to variable"},{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":17,"line":22},"start":{"character":10,"line":22}},"tweakID":"RawStringLiteral"}],"command":"clangd.applyTweak","title":"Convert to raw string"},"kind":"refactor","title":"Convert to raw string"}]},"dur":71.926999986171722,"name":"textDocument/codeAction","tid":241390,"ts":292032453.93300003},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":292032914.412},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(44)"},"name":"Log","tid":241390,"ts":292304901.22299999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.1240000128746033,"name":"Queued:SemanticHighlights","tid":241390,"ts":292304940.87900001},{"pid":0,"ph":"s","cat":"mock_cat","id":52,"name":"Context crosses threads","tid":241390,"ts":292304887.958},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":52,"name":"Context crosses threads","tid":250490,"ts":292304992.82800001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":12.062999963760376,"name":"getConfig","tid":250490,"ts":292304997.87800002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(44) 0 ms"},"name":"Log","tid":250490,"ts":292305335.99299997},{"pid":0,"ph":"X","args":{},"dur":433.00600004196167,"name":"SemanticHighlights","tid":250490,"ts":292304982.72899997},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"data":[8,5,8,3,131073,1,5,9,3,131073,1,5,8,3,131073,0,21,8,2,16403,1,5,8,3,131073,1,5,13,3,131073,0,20,11,2,16387,0,18,8,2,16387,0,14,4,2,16387,4,5,7,3,131075,1,2,3,19,131072,1,2,5,19,131072,0,6,12,19,131072,0,14,3,19,131072,3,2,3,19,131072,1,2,6,3,131584,2,2,6,19,131072,5,5,18,3,131073,2,13,8,0,65539,0,11,4,19,131072,1,13,12,0,65539,0,15,4,19,131072,1,13,8,0,65539,0,11,4,19,131072,1,0,4,8,66048,0,6,8,0,131075,0,11,4,19,131072,1,11,13,0,65539,3,12,11,3,131073,1,11,10,3,65539,0,15,4,2,16387,0,12,4,2,16387,1,17,6,8,131584,0,7,5,1,16403,1,22,11,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,41,4,19,131072,2,8,1,1,16387,1,13,1,1,16384,0,2,1,21,0,0,2,11,3,131584,0,12,4,2,16384,0,6,4,2,16384,0,21,5,1,16400,0,7,4,19,131072,0,7,2,21,0,0,3,1,21,0,1,14,1,1,16384,1,18,18,3,131072,1,18,8,0,65536,0,9,1,21,0,0,2,6,0,131584,1,18,6,3,131584,0,7,6,0,131584,0,14,1,21,0,0,1,13,0,65536,1,18,12,0,65536,0,13,1,21,0,0,2,6,0,131584,1,18,8,0,131072,0,9,1,21,0,0,2,5,3,131584,0,6,6,0,131584,0,13,11,3,131072,1,16,8,0,65536,0,9,1,21,0,0,2,6,0,131584,2,10,6,3,131584,0,49,4,2,16384,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,4,3,131584,10,12,8,3,65539,1,8,8,0,65536,0,9,2,21,0,0,3,4,19,131072,1,6,3,19,131072,3,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,7,8,0,65536,1,4,6,19,131072,0,8,2,1,16384,0,25,8,0,65536,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,9,4,1,16387,0,7,5,3,131584,0,7,2,1,18432,2,4,3,19,131072,0,36,8,0,65536,0,10,4,1,16384,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,8,3,1,16387,0,6,5,3,131584,0,7,13,3,131072,0,15,12,19,131072,0,15,4,1,16384,0,9,2,1,18432,1,4,6,19,131072,0,8,3,1,16384,0,4,2,21,0,2,4,6,3,131584,0,8,2,1,18432,1,11,4,1,16384,3,13,8,18,66048,0,9,3,0,65555,7,12,7,3,65539,2,11,12,19,131072,9,5,8,3,131075,2,2,6,3,131584,0,7,13,3,131072,0,14,12,19,131072,0,15,3,0,65552,0,12,3,0,65552,3,2,7,3,65536,3,5,12,3,131075,0,18,4,2,16387,0,12,4,2,16387,3,4,10,3,65536,0,12,4,2,16384,0,6,4,2,18432,2,4,9,3,131072,3,4,8,3,131072,0,10,8,0,65536,3,4,8,3,131072,2,4,8,3,131072,2,9,8,1,16387,0,11,8,3,65536,7,4,7,3,131072],"resultId":"2"}},"dur":59.092999994754791,"name":"textDocument/semanticTokens/full","tid":241390,"ts":292304887.958},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":292305508.662},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(45)"},"name":"Log","tid":241390,"ts":292493798.11699998},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250524},{"pid":0,"ph":"X","args":{},"dur":13.555999994277954,"name":"WaitForFreeSemaphoreSlot","tid":250524,"ts":292493904.33899999},{"pid":0,"ph":"s","cat":"mock_cat","id":53,"name":"Context crosses threads","tid":241390,"ts":292493779.551},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":53,"name":"Context crosses threads","tid":250524,"ts":292493935.94999999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":16.98199999332428,"name":"getConfig","tid":250524,"ts":292493929.79799998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(45) 0 ms"},"name":"Log","tid":250524,"ts":292494315.98500001},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endCharacter":67,"endLine":19,"kind":"region","startCharacter":8,"startLine":18},{"endCharacter":66,"endLine":19,"kind":"region","startCharacter":26,"startLine":18},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":100.01099997758865,"name":"textDocument/foldingRange","tid":241390,"ts":292493779.551},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(46)"},"name":"Log","tid":241390,"ts":292539605.82700002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.1940000057220459,"name":"Queued:codeAction","tid":241390,"ts":292539666.55299997},{"pid":0,"ph":"s","cat":"mock_cat","id":54,"name":"Context crosses threads","tid":241390,"ts":292539578.70499998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":54,"name":"Context crosses threads","tid":250490,"ts":292539719.98500001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":20.660000026226044,"name":"getConfig","tid":250490,"ts":292539774.829},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(46) 0 ms"},"name":"Log","tid":250490,"ts":292540027.472},{"pid":0,"ph":"X","args":{},"dur":407.25700002908707,"name":"codeAction","tid":250490,"ts":292539705.417},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":17,"line":22},"start":{"character":10,"line":22}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":17,"line":22},"start":{"character":10,"line":22}},"tweakID":"ExtractVariable"}],"command":"clangd.applyTweak","title":"Extract subexpression to variable"},"kind":"refactor","title":"Extract subexpression to variable"},{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":17,"line":22},"start":{"character":10,"line":22}},"tweakID":"RawStringLiteral"}],"command":"clangd.applyTweak","title":"Convert to raw string"},"kind":"refactor","title":"Convert to raw string"}]},"dur":96.905000030994415,"name":"textDocument/codeAction","tid":241390,"ts":292539578.70499998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":292540159.48299998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(47)"},"name":"Log","tid":241390,"ts":292644230.80599999},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250525},{"pid":0,"ph":"X","args":{},"dur":11.582000017166138,"name":"WaitForFreeSemaphoreSlot","tid":250525,"ts":292644317.963},{"pid":0,"ph":"s","cat":"mock_cat","id":55,"name":"Context crosses threads","tid":241390,"ts":292644219.014},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":55,"name":"Context crosses threads","tid":250525,"ts":292644343.51200002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":16.590999960899353,"name":"getConfig","tid":250525,"ts":292644338.33200002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(47) 0 ms"},"name":"Log","tid":250525,"ts":292644701.63499999},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endCharacter":67,"endLine":19,"kind":"region","startCharacter":8,"startLine":18},{"endCharacter":66,"endLine":19,"kind":"region","startCharacter":26,"startLine":18},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":79.000999987125397,"name":"textDocument/foldingRange","tid":241390,"ts":292644219.014},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(48)"},"name":"Log","tid":241390,"ts":292834318.68400002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.9440000057220459,"name":"Queued:InlayHints","tid":241390,"ts":292834352.19800001},{"pid":0,"ph":"s","cat":"mock_cat","id":56,"name":"Context crosses threads","tid":241390,"ts":292834302.97399998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":56,"name":"Context crosses threads","tid":250490,"ts":292834396.20200002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.290000021457672,"name":"getConfig","tid":250490,"ts":292834401.26099998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(48) 0 ms"},"name":"Log","tid":250490,"ts":292835126.40600002},{"pid":0,"ph":"X","args":{},"dur":1069.9819999933243,"name":"InlayHints","tid":250490,"ts":292834389.75999999},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":17,"line":115},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":9,"line":22}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":41}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":41}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":41}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":41}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":41}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":42}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":42}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":42}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":42}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":42}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":43}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":43}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":43}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":43}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":43}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":44}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":44}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":44}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":44}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":44}},{"label":[{"value":"[4]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":45}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":45}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":45}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":45}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":45}},{"label":[{"value":"[5]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":46}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":46}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":46}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":46}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":48,"line":46}},{"kind":2,"label":[{"value":"shortopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":41,"line":49}},{"kind":2,"label":[{"value":"longopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":56,"line":49}},{"kind":2,"label":[{"value":"longind:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":63,"line":49}},{"kind":2,"label":[{"value":"s:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":25,"line":53}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":33,"line":53}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":35,"line":55}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":42,"line":55}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":58}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":59}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":60}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":61}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":62}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":63}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":64}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":65}},{"kind":2,"label":[{"value":"status:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":66}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":81}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":32,"line":81}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":84}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":84}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":84}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":23,"line":85}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":89}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":89}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":89}},{"kind":2,"label":[{"value":"ptr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":90}},{"kind":2,"label":[{"value":"paddr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":36,"line":90}},{"kind":2,"label":[{"value":"n:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":57,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":60,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":12,"line":93}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":98}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":99}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":100}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":101}}]},"dur":55.035000026226044,"name":"textDocument/inlayHint","tid":241390,"ts":292834302.97399998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":292835625.31800002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(49)"},"name":"Log","tid":241390,"ts":293550644.80299997},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.7930000424385071,"name":"Queued:codeAction","tid":241390,"ts":293550687.64499998},{"pid":0,"ph":"s","cat":"mock_cat","id":57,"name":"Context crosses threads","tid":241390,"ts":293550621.22799999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":57,"name":"Context crosses threads","tid":250490,"ts":293550734.33399999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.199000000953674,"name":"getConfig","tid":250490,"ts":293550738.94300002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(49) 0 ms"},"name":"Log","tid":250490,"ts":293551029.67799997},{"pid":0,"ph":"X","args":{},"dur":345.56999999284744,"name":"codeAction","tid":250490,"ts":293550725.45700002},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":11,"line":24},"start":{"character":11,"line":24}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":73.430000007152557,"name":"textDocument/codeAction","tid":241390,"ts":293550621.22799999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":293551119.27899998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/hover(50)"},"name":"Log","tid":241390,"ts":293709051.17199999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.7630000114440918,"name":"Queued:Hover","tid":241390,"ts":293709092.75199997},{"pid":0,"ph":"s","cat":"mock_cat","id":58,"name":"Context crosses threads","tid":241390,"ts":293709032.24599999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":58,"name":"Context crosses threads","tid":250490,"ts":293709161.27200001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":11.200999975204468,"name":"getConfig","tid":250490,"ts":293709165.59100002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/hover(50) 0 ms"},"name":"Log","tid":250490,"ts":293709303.81400001},{"pid":0,"ph":"X","args":{},"dur":174.9539999961853,"name":"Hover","tid":250490,"ts":293709154.42900002},{"pid":0,"ph":"X","args":{"Params":{"position":{"character":28,"line":23},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":null},"dur":66.517000019550323,"name":"textDocument/hover","tid":241390,"ts":293709032.24599999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":293709347.66799998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/hover(51)"},"name":"Log","tid":241390,"ts":294191711.72100002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.8830000162124634,"name":"Queued:Hover","tid":241390,"ts":294191777.67699999},{"pid":0,"ph":"s","cat":"mock_cat","id":59,"name":"Context crosses threads","tid":241390,"ts":294191698.87599999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":59,"name":"Context crosses threads","tid":250490,"ts":294191856.838},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.829999983310699,"name":"getConfig","tid":250490,"ts":294191860.986},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/hover(51) 0 ms"},"name":"Log","tid":250490,"ts":294192000.67199999},{"pid":0,"ph":"X","args":{},"dur":177.50900000333786,"name":"Hover","tid":250490,"ts":294191848.542},{"pid":0,"ph":"X","args":{"Params":{"position":{"character":40,"line":23},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":null},"dur":84.250999987125397,"name":"textDocument/hover","tid":241390,"ts":294191698.87599999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":294192046.15899998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/hover(52)"},"name":"Log","tid":241390,"ts":294358324.30299997},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.7129999995231628,"name":"Queued:Hover","tid":241390,"ts":294358364.37900001},{"pid":0,"ph":"s","cat":"mock_cat","id":60,"name":"Context crosses threads","tid":241390,"ts":294358308.00199997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":60,"name":"Context crosses threads","tid":250490,"ts":294358433.86199999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.5,"name":"getConfig","tid":250490,"ts":294358438.20999998},{"pid":0,"ph":"X","args":{},"dur":1.6829999685287476,"name":"MemIndex lookup","tid":250490,"ts":294358715.67000002},{"pid":0,"ph":"X","args":{},"dur":5.531000018119812,"name":"Dex lookup","tid":250490,"ts":294358740.01599997},{"pid":0,"ph":"X","args":{},"dur":37.291000008583069,"name":"MergedIndex lookup","tid":250490,"ts":294358713.065},{"pid":0,"ph":"X","args":{},"dur":0.77100002765655518,"name":"MemIndex lookup","tid":250490,"ts":294358756.04699999},{"pid":0,"ph":"X","args":{},"dur":1.3420000076293945,"name":"ProjectAwareIndex::lookup","tid":250490,"ts":294358761.227},{"pid":0,"ph":"X","args":{},"dur":13.235000014305115,"name":"MergedIndex lookup","tid":250490,"ts":294358754.89499998},{"pid":0,"ph":"X","args":{},"dur":65.014999985694885,"name":"MergedIndex lookup","tid":250490,"ts":294358710.30900002},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":294358905.65200001},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":294358929.13700002},{"pid":0,"ph":"X","args":{},"dur":99.509999990463257,"name":"Hover::maybeAddSymbolProviders","tid":250490,"ts":294358862.38999999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/hover(52) 1 ms"},"name":"Log","tid":250490,"ts":294359536.51599997},{"pid":0,"ph":"X","args":{},"dur":1149.8240000009537,"name":"Hover","tid":250490,"ts":294358427.54000002},{"pid":0,"ph":"X","args":{"Params":{"position":{"character":33,"line":22},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"contents":{"kind":"markdown","value":"### function `printf`  \nprovided by `<cstdio>`  \n\n---\n→ `int`  \nParameters:  \n- `const char *__restrict __format`\n\nWrite formatted output to stdout.  \nThis function is a possible cancellation point and therefore not marked with \\_\\_THROW.  \n\n---\n```cpp\nextern int printf(const char *__restrict __format, ...)\n```"},"range":{"end":{"character":34,"line":22},"start":{"character":33,"line":22}}}},"dur":68.300000011920929,"name":"textDocument/hover","tid":241390,"ts":294358308.00199997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":294359638.27999997},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/hover(53)"},"name":"Log","tid":241390,"ts":294985849.35399997},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.5249999761581421,"name":"Queued:Hover","tid":241390,"ts":294985904.41900003},{"pid":0,"ph":"s","cat":"mock_cat","id":61,"name":"Context crosses threads","tid":241390,"ts":294985834.47500002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":61,"name":"Context crosses threads","tid":250490,"ts":294985950.07599998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":12.312999963760376,"name":"getConfig","tid":250490,"ts":294985956.26800001},{"pid":0,"ph":"X","args":{},"dur":1.2619999647140503,"name":"MemIndex lookup","tid":250490,"ts":294986188.90200001},{"pid":0,"ph":"X","args":{},"dur":7.484000027179718,"name":"Dex lookup","tid":250490,"ts":294986202.648},{"pid":0,"ph":"X","args":{},"dur":30.297999978065491,"name":"MergedIndex lookup","tid":250490,"ts":294986186.597},{"pid":0,"ph":"X","args":{},"dur":0.88200002908706665,"name":"MemIndex lookup","tid":250490,"ts":294986226.78399998},{"pid":0,"ph":"X","args":{},"dur":1.9339999556541443,"name":"ProjectAwareIndex::lookup","tid":250490,"ts":294986235.42000002},{"pid":0,"ph":"X","args":{},"dur":18.355000019073486,"name":"MergedIndex lookup","tid":250490,"ts":294986224.991},{"pid":0,"ph":"X","args":{},"dur":67.328999996185303,"name":"MergedIndex lookup","tid":250490,"ts":294986183.38099998},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":294986331.75400001},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":294986352.75400001},{"pid":0,"ph":"X","args":{},"dur":73.761000037193298,"name":"Hover::maybeAddSymbolProviders","tid":250490,"ts":294986295.99599999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/hover(53) 0 ms"},"name":"Log","tid":250490,"ts":294986807.36199999},{"pid":0,"ph":"X","args":{},"dur":919.74500000476837,"name":"Hover","tid":250490,"ts":294985941.26899999},{"pid":0,"ph":"X","args":{"Params":{"position":{"character":34,"line":22},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"contents":{"kind":"markdown","value":"### function `printf`  \nprovided by `<cstdio>`  \n\n---\n→ `int`  \nParameters:  \n- `const char *__restrict __format`\n\nWrite formatted output to stdout.  \nThis function is a possible cancellation point and therefore not marked with \\_\\_THROW.  \n\n---\n```cpp\nextern int printf(const char *__restrict __format, ...)\n```"},"range":{"end":{"character":35,"line":22},"start":{"character":34,"line":22}}}},"dur":78.048999965190887,"name":"textDocument/hover","tid":241390,"ts":294985834.47500002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":294986897.44400001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(54)"},"name":"Log","tid":241390,"ts":295367062.31699997},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.9739999771118164,"name":"Queued:codeAction","tid":241390,"ts":295367098.46600002},{"pid":0,"ph":"s","cat":"mock_cat","id":62,"name":"Context crosses threads","tid":241390,"ts":295367046.18599999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":62,"name":"Context crosses threads","tid":250490,"ts":295367146.20700002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":13.456000030040741,"name":"getConfig","tid":250490,"ts":295367194.69999999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(54) 0 ms"},"name":"Log","tid":250490,"ts":295367278.82999998},{"pid":0,"ph":"X","args":{},"dur":176.71700000762939,"name":"codeAction","tid":250490,"ts":295367136.55900002},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":18,"line":27},"start":{"character":18,"line":27}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":58.541999995708466,"name":"textDocument/codeAction","tid":241390,"ts":295367046.18599999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":295367350.708},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(55)"},"name":"Log","tid":241390,"ts":296306786.741},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.4869999885559082,"name":"Queued:codeAction","tid":241390,"ts":296306838.73000002},{"pid":0,"ph":"s","cat":"mock_cat","id":63,"name":"Context crosses threads","tid":241390,"ts":296306769.35799998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":63,"name":"Context crosses threads","tid":250490,"ts":296306915.27600002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":9.0769999623298645,"name":"getConfig","tid":250490,"ts":296306919.86500001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(55) 0 ms"},"name":"Log","tid":250490,"ts":296307073.23799998},{"pid":0,"ph":"X","args":{},"dur":191.02499997615814,"name":"codeAction","tid":250490,"ts":296306909.074},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":20,"line":18},"start":{"character":8,"line":18}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":82.146000027656555,"name":"textDocument/codeAction","tid":241390,"ts":296306769.35799998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":296307123.44300002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(56)"},"name":"Log","tid":241390,"ts":296844849.19199997},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.675000011920929,"name":"Queued:codeAction","tid":241390,"ts":296844915.65899998},{"pid":0,"ph":"s","cat":"mock_cat","id":64,"name":"Context crosses threads","tid":241390,"ts":296844809.63599998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":64,"name":"Context crosses threads","tid":250490,"ts":296844974.68099999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":154.65499997138977,"name":"getConfig","tid":250490,"ts":296844979.05000001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(56) 0 ms"},"name":"Log","tid":250490,"ts":296845387.259},{"pid":0,"ph":"X","args":{},"dur":473.50400000810623,"name":"codeAction","tid":250490,"ts":296844968.63999999},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":4,"line":18},"start":{"character":4,"line":18}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":4,"line":18},"start":{"character":4,"line":18}},"tweakID":"ExpandMacro"}],"command":"clangd.applyTweak","title":"Expand macro 'IFDEF'"},"kind":"refactor","title":"Expand macro 'IFDEF'"},{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":4,"line":18},"start":{"character":4,"line":18}},"tweakID":"ExtractFunction"}],"command":"clangd.applyTweak","title":"Extract to function"},"kind":"refactor","title":"Extract to function"}]},"dur":114.79900002479553,"name":"textDocument/codeAction","tid":241390,"ts":296844809.63599998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":296845502.338},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/hover(57)"},"name":"Log","tid":241390,"ts":296908335.12199998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.7239999771118164,"name":"Queued:Hover","tid":241390,"ts":296908388.00300002},{"pid":0,"ph":"s","cat":"mock_cat","id":65,"name":"Context crosses threads","tid":241390,"ts":296908314.48299998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":65,"name":"Context crosses threads","tid":250490,"ts":296908448.72899997},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":8.9979999661445618,"name":"getConfig","tid":250490,"ts":296908452.96700001},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":296908781.24400002},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":296908800.93199998},{"pid":0,"ph":"X","args":{},"dur":74.241999983787537,"name":"Hover::maybeAddSymbolProviders","tid":250490,"ts":296908752.449},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/hover(57) 1 ms"},"name":"Log","tid":250490,"ts":296909719.90600002},{"pid":0,"ph":"X","args":{},"dur":1339.2860000133514,"name":"Hover","tid":250490,"ts":296908442.72799999},{"pid":0,"ph":"X","args":{"Params":{"position":{"character":3,"line":17},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"contents":{"kind":"markdown","value":"### macro `Log`  \nprovided by `\"debug.h\"`  \n\n---\n```cpp\n#define Log(format, ...)                                                       \\\n  _Log(ANSI_FMT(\"[%s:%d %s] \" format, ANSI_FG_BLUE) \"\\n\", __FILE__, __LINE__,  \\\n       __func__, ##__VA_ARGS__)\n\n// Expands to\ndo {\n  printf(\"\\33[1;34m\"\n         \"[%s:%d %s] \"\n         \"Trace: %s\"\n         \"\\33[0m\"\n         \"\\n\",\n         \"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/\"\n         \"ysyx-workbench/npc/csrc/monitor/monitor.cpp\",\n         18, __func__,\n         \"\\33[1;32m\"\n         \"ON\"\n         \"\\33[0m\");\n  do {\n    extern FILE *log_fp;\n    extern bool log_enable();\n    if (log_enable()) {\n      fprintf(log_fp,\n              \"\\33[1;34m\"\n              \"[%s:%d %s] \"\n              \"Trace: %s\"\n              \"\\33[0m\"\n              \"\\n\",\n              \"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/\"\n              \"ysyx-workbench/npc/csrc/monitor/monitor.cpp\",\n              18, __func__,\n              \"\\33[1;32m\"\n              \"ON\"\n              \"\\33[0m\");\n      fflush(log_fp);\n    }\n  } while (0);\n} while (0)\n```"},"range":{"end":{"character":5,"line":17},"start":{"character":2,"line":17}}}},"dur":78.961000025272369,"name":"textDocument/hover","tid":241390,"ts":296908314.48299998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":296909809.76700002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(58)"},"name":"Log","tid":241390,"ts":297202416.67500001},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.7630000114440918,"name":"Queued:codeAction","tid":241390,"ts":297202450.67000002},{"pid":0,"ph":"s","cat":"mock_cat","id":66,"name":"Context crosses threads","tid":241390,"ts":297202400.25400001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":66,"name":"Context crosses threads","tid":250490,"ts":297202514.53200001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":11.743000030517578,"name":"getConfig","tid":250490,"ts":297202519.98199999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(58) 0 ms"},"name":"Log","tid":250490,"ts":297202781.67199999},{"pid":0,"ph":"X","args":{},"dur":330.87199997901917,"name":"codeAction","tid":250490,"ts":297202507.46799999},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":3,"line":17},"start":{"character":3,"line":17}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":3,"line":17},"start":{"character":3,"line":17}},"tweakID":"ExpandMacro"}],"command":"clangd.applyTweak","title":"Expand macro 'Log'"},"kind":"refactor","title":"Expand macro 'Log'"},{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":3,"line":17},"start":{"character":3,"line":17}},"tweakID":"ExtractFunction"}],"command":"clangd.applyTweak","title":"Extract to function"},"kind":"refactor","title":"Extract to function"}]},"dur":56.978999972343445,"name":"textDocument/codeAction","tid":241390,"ts":297202400.25400001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":297202871.53299999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/didChange"},"name":"Log","tid":241390,"ts":298205226.64499998},{"pid":0,"ph":"s","cat":"mock_cat","id":67,"name":"Context crosses threads","tid":241390,"ts":298205204.50300002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":67,"name":"Context crosses threads","tid":250490,"ts":298205304.06300002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":298205321.45599997},{"pid":0,"ph":"X","args":{"next_request":"Update","sleep_ms":49},"dur":50056.038999974728,"name":"Debounce","tid":250490,"ts":298205292.23000002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.8939999938011169,"name":"Queued:Update","tid":241390,"ts":298205275.40799999},{"pid":0,"ph":"s","cat":"mock_cat","id":68,"name":"Context crosses threads","tid":241390,"ts":298205204.50300002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":68,"name":"Context crosses threads","tid":250490,"ts":298255385.48000002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":9.7580000162124634,"name":"getConfig","tid":250490,"ts":298255390.74000001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":5.449999988079071,"name":"getConfig","tid":250490,"ts":298255410.82800001},{"pid":0,"ph":"i","args":{"Message":"Failed to find compilation database for /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"name":"Log","tid":250490,"ts":298255431.08700001},{"pid":0,"ph":"X","args":{},"dur":50.60699999332428,"name":"AdjustCompileFlags","tid":250490,"ts":298255457.54699999},{"pid":0,"ph":"i","args":{"Message":"ASTWorker building file /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp version 2 with command clangd fallback\n[/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor]\n/usr/lib/llvm-14/bin/clang -I/home/<USER>/Desktop/ysyx-workbench/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/nemu/src/isa/riscv32/include/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/build/obj_dir/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/klib/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/src/platform/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include -I/home/<USER>/Desktop/ysyx-workbench/nvboard/include/ -I/usr/include/SDL2/ -I/usr/share/verilator/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/csrc/include -include=/headers/file.h -I/other/headers -resource-dir=/home/<USER>/.config/Code/User/globalStorage/llvm-vs-code-extensions.vscode-clangd/install/19.1.2/clangd_19.1.2/lib/clang/19 -- /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"name":"Log","tid":250490,"ts":298255823.44599998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250491,"ts":298257428.51899999},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":1409.3199999928474,"name":"CreatePreamblePatch","tid":250490,"ts":298257421.26499999},{"pid":0,"ph":"X","args":{},"dur":145.11699998378754,"name":"ClangTidyOpts","tid":250490,"ts":298258893.48500001},{"pid":0,"ph":"X","args":{},"dur":1184.2800000309944,"name":"ClangTidyInit","tid":250490,"ts":298259526.28299999},{"pid":0,"ph":"X","args":{"results":0},"dur":2.9860000014305115,"name":"MemIndex fuzzyFind","tid":250490,"ts":298264509.88499999},{"pid":0,"ph":"X","args":{"query":"(LIMIT 10000 (& T=cpu S= ?=Restricted For Code Completion))"},"dur":20.959999978542328,"name":"Dex fuzzyFind","tid":250490,"ts":298264527.21799999},{"pid":0,"ph":"X","args":{"dynamic":0,"merged":0,"static":2,"static_dropped":0},"dur":46.307999968528748,"name":"MergedIndex fuzzyFind","tid":250490,"ts":298264508.28200001},{"pid":0,"ph":"X","args":{"results":0},"dur":1.4029999971389771,"name":"MemIndex fuzzyFind","tid":250490,"ts":298264561.704},{"pid":0,"ph":"X","args":{},"dur":1.2220000028610229,"name":"ProjectAwareIndex::fuzzyFind","tid":250490,"ts":298264567.27499998},{"pid":0,"ph":"X","args":{"dynamic":0,"merged":0,"static":0,"static_dropped":0},"dur":11.65200001001358,"name":"MergedIndex fuzzyFind","tid":250490,"ts":298264560.301},{"pid":0,"ph":"X","args":{"dynamic":2,"merged":0,"static":0,"static_dropped":0},"dur":72.01800000667572,"name":"MergedIndex fuzzyFind","tid":250490,"ts":298264505.81699997},{"pid":0,"ph":"X","args":{},"dur":1138.5529999732971,"name":"ClangTidyMatch","tid":250490,"ts":298265236.59200001},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":298266409.36000001},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":298266432.27399999},{"pid":0,"ph":"X","args":{},"dur":3.2760000228881836,"name":"IncludeCleaner::getUnused","tid":250490,"ts":298266849.31999999},{"pid":0,"ph":"X","args":{},"dur":294.11100000143051,"name":"include_cleaner::walkUsed","tid":250490,"ts":298266564.98799998},{"pid":0,"ph":"X","args":{},"dur":364.34500002861023,"name":"IncludeCleaner::issueIncludeCleanerDiagnostics","tid":250490,"ts":298266865.87199998},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":9953.5270000100136,"name":"BuildAST","tid":250490,"ts":298257376.36000001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/publishDiagnostics"},"name":"Log","tid":250490,"ts":298267941.074},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/inactiveRegions"},"name":"Log","tid":250490,"ts":298267988.79500002},{"pid":0,"ph":"X","args":{},"dur":206.7039999961853,"name":"ASTSignals::derive","tid":250490,"ts":298268005.97799999},{"pid":0,"ph":"X","args":{},"dur":898.17400002479553,"name":"Running main AST callback","tid":250490,"ts":298267360.426},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":15.459999978542328,"name":"getConfig","tid":250490,"ts":298268280.10100001},{"pid":0,"ph":"X","args":{},"dur":62.708999991416931,"name":"Build AST","tid":250490,"ts":298268277.85699999},{"pid":0,"ph":"X","args":{},"dur":12894.060000002384,"name":"Update","tid":250490,"ts":298255379.68900001},{"pid":0,"ph":"X","args":{"Params":{"contentChanges":[{"range":{"end":{"character":2,"line":17},"start":{"character":2,"line":17}},"rangeLength":0,"text":"// "}],"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","version":2}}},"dur":78.729999959468842,"name":"textDocument/didChange","tid":241390,"ts":298205204.50300002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":298268378.05800003},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(59)"},"name":"Log","tid":241390,"ts":298412242.46100003},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250604},{"pid":0,"ph":"X","args":{},"dur":18.095000028610229,"name":"WaitForFreeSemaphoreSlot","tid":250604,"ts":298412339.31599998},{"pid":0,"ph":"s","cat":"mock_cat","id":69,"name":"Context crosses threads","tid":241390,"ts":298412230.99000001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":69,"name":"Context crosses threads","tid":250604,"ts":298412371.91799998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":16.671999990940094,"name":"getConfig","tid":250604,"ts":298412366.097},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(59) 0 ms"},"name":"Log","tid":250604,"ts":298412714.63300002},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endCharacter":67,"endLine":19,"kind":"region","startCharacter":8,"startLine":18},{"endCharacter":66,"endLine":19,"kind":"region","startCharacter":26,"startLine":18},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":77.35699999332428,"name":"textDocument/foldingRange","tid":241390,"ts":298412230.99000001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(60)"},"name":"Log","tid":241390,"ts":298484615.324},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.1260000467300415,"name":"Queued:InlayHints","tid":241390,"ts":298484654.40899998},{"pid":0,"ph":"s","cat":"mock_cat","id":70,"name":"Context crosses threads","tid":241390,"ts":298484585.42699999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":70,"name":"Context crosses threads","tid":250490,"ts":298484698.74299997},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":13.515999972820282,"name":"getConfig","tid":250490,"ts":298484748.02700001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(60) 1 ms"},"name":"Log","tid":250490,"ts":298485719.51200002},{"pid":0,"ph":"X","args":{},"dur":1538.7669999599457,"name":"InlayHints","tid":250490,"ts":298484690.93800002},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":17,"line":115},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":9,"line":22}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":41}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":41}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":41}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":41}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":41}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":42}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":42}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":42}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":42}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":42}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":43}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":43}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":43}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":43}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":43}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":44}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":44}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":44}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":44}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":44}},{"label":[{"value":"[4]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":45}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":45}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":45}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":45}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":45}},{"label":[{"value":"[5]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":46}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":46}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":46}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":46}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":48,"line":46}},{"kind":2,"label":[{"value":"shortopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":41,"line":49}},{"kind":2,"label":[{"value":"longopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":56,"line":49}},{"kind":2,"label":[{"value":"longind:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":63,"line":49}},{"kind":2,"label":[{"value":"s:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":25,"line":53}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":33,"line":53}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":35,"line":55}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":42,"line":55}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":58}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":59}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":60}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":61}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":62}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":63}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":64}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":65}},{"kind":2,"label":[{"value":"status:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":66}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":81}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":32,"line":81}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":84}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":84}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":84}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":23,"line":85}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":89}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":89}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":89}},{"kind":2,"label":[{"value":"ptr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":90}},{"kind":2,"label":[{"value":"paddr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":36,"line":90}},{"kind":2,"label":[{"value":"n:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":57,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":60,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":12,"line":93}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":98}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":99}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":100}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":101}}]},"dur":80.643999993801117,"name":"textDocument/inlayHint","tid":241390,"ts":298484585.42699999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":298486457.19},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(61)"},"name":"Log","tid":241390,"ts":298509796.26499999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.7170000076293945,"name":"Queued:SemanticHighlights","tid":241390,"ts":298509845.55900002},{"pid":0,"ph":"s","cat":"mock_cat","id":71,"name":"Context crosses threads","tid":241390,"ts":298509777.139},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":71,"name":"Context crosses threads","tid":250490,"ts":298509900.023},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":20.208999991416931,"name":"getConfig","tid":250490,"ts":298509907.958},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(61) 0 ms"},"name":"Log","tid":250490,"ts":298510425.10500002},{"pid":0,"ph":"X","args":{},"dur":647.44499999284744,"name":"SemanticHighlights","tid":250490,"ts":298509889.213},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"data":[8,5,8,3,131073,1,5,9,3,131073,1,5,8,3,131073,0,21,8,2,16403,1,5,8,3,131073,1,5,13,3,131073,0,20,11,2,16387,0,18,8,2,16387,0,14,4,2,16387,4,5,7,3,131075,2,2,5,19,131072,0,6,12,19,131072,0,14,3,19,131072,3,2,3,19,131072,1,2,6,3,131584,2,2,6,19,131072,5,5,18,3,131073,2,13,8,0,65539,0,11,4,19,131072,1,13,12,0,65539,0,15,4,19,131072,1,13,8,0,65539,0,11,4,19,131072,1,0,4,8,66048,0,6,8,0,131075,0,11,4,19,131072,1,11,13,0,65539,3,12,11,3,131073,1,11,10,3,65539,0,15,4,2,16387,0,12,4,2,16387,1,17,6,8,131584,0,7,5,1,16403,1,22,11,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,41,4,19,131072,2,8,1,1,16387,1,13,1,1,16384,0,2,1,21,0,0,2,11,3,131584,0,12,4,2,16384,0,6,4,2,16384,0,21,5,1,16400,0,7,4,19,131072,0,7,2,21,0,0,3,1,21,0,1,14,1,1,16384,1,18,18,3,131072,1,18,8,0,65536,0,9,1,21,0,0,2,6,0,131584,1,18,6,3,131584,0,7,6,0,131584,0,14,1,21,0,0,1,13,0,65536,1,18,12,0,65536,0,13,1,21,0,0,2,6,0,131584,1,18,8,0,131072,0,9,1,21,0,0,2,5,3,131584,0,6,6,0,131584,0,13,11,3,131072,1,16,8,0,65536,0,9,1,21,0,0,2,6,0,131584,2,10,6,3,131584,0,49,4,2,16384,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,4,3,131584,10,12,8,3,65539,1,8,8,0,65536,0,9,2,21,0,0,3,4,19,131072,1,6,3,19,131072,3,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,7,8,0,65536,1,4,6,19,131072,0,8,2,1,16384,0,25,8,0,65536,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,9,4,1,16387,0,7,5,3,131584,0,7,2,1,18432,2,4,3,19,131072,0,36,8,0,65536,0,10,4,1,16384,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,8,3,1,16387,0,6,5,3,131584,0,7,13,3,131072,0,15,12,19,131072,0,15,4,1,16384,0,9,2,1,18432,1,4,6,19,131072,0,8,3,1,16384,0,4,2,21,0,2,4,6,3,131584,0,8,2,1,18432,1,11,4,1,16384,3,13,8,18,66048,0,9,3,0,65555,7,12,7,3,65539,2,11,12,19,131072,9,5,8,3,131075,2,2,6,3,131584,0,7,13,3,131072,0,14,12,19,131072,0,15,3,0,65552,0,12,3,0,65552,3,2,7,3,65536,3,5,12,3,131075,0,18,4,2,16387,0,12,4,2,16387,3,4,10,3,65536,0,12,4,2,16384,0,6,4,2,18432,2,4,9,3,131072,3,4,8,3,131072,0,10,8,0,65536,3,4,8,3,131072,2,4,8,3,131072,2,9,8,1,16387,0,11,8,3,65536,7,4,7,3,131072],"resultId":"3"}},"dur":79.300999999046326,"name":"textDocument/semanticTokens/full","tid":241390,"ts":298509777.139},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":298510611.23100001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(62)"},"name":"Log","tid":241390,"ts":298521103.76700002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":4.1780000329017639,"name":"Queued:codeAction","tid":241390,"ts":298521208.84799999},{"pid":0,"ph":"s","cat":"mock_cat","id":72,"name":"Context crosses threads","tid":241390,"ts":298521078.389},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":72,"name":"Context crosses threads","tid":250490,"ts":298521270.52600002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":15.458999991416931,"name":"getConfig","tid":250490,"ts":298521277.81},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(62) 0 ms"},"name":"Log","tid":250490,"ts":298521366.15799999},{"pid":0,"ph":"X","args":{},"dur":142.65200001001358,"name":"codeAction","tid":250490,"ts":298521260.597},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":6,"line":17},"start":{"character":6,"line":17}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":144.21499997377396,"name":"textDocument/codeAction","tid":241390,"ts":298521078.389},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":298521445.22899997},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(63)"},"name":"Log","tid":241390,"ts":298562475.60000002},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250605},{"pid":0,"ph":"X","args":{},"dur":11.030999958515167,"name":"WaitForFreeSemaphoreSlot","tid":250605,"ts":298562570.03100002},{"pid":0,"ph":"s","cat":"mock_cat","id":73,"name":"Context crosses threads","tid":241390,"ts":298562460.94300002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":73,"name":"Context crosses threads","tid":250605,"ts":298562600.62900001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":17.333000004291534,"name":"getConfig","tid":250605,"ts":298562594.18699998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(63) 0 ms"},"name":"Log","tid":250605,"ts":298562992.07599998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endCharacter":67,"endLine":19,"kind":"region","startCharacter":8,"startLine":18},{"endCharacter":66,"endLine":19,"kind":"region","startCharacter":26,"startLine":18},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":93.257999956607819,"name":"textDocument/foldingRange","tid":241390,"ts":298562460.94300002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentSymbol(64)"},"name":"Log","tid":241390,"ts":298711637.926},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.175000011920929,"name":"Queued:DocumentSymbols","tid":241390,"ts":298711733.59799999},{"pid":0,"ph":"s","cat":"mock_cat","id":74,"name":"Context crosses threads","tid":241390,"ts":298711618.65899998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":74,"name":"Context crosses threads","tid":250490,"ts":298711808.58099997},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":19.858000040054321,"name":"getConfig","tid":250490,"ts":298711820.46399999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentSymbol(64) 0 ms"},"name":"Log","tid":250490,"ts":298712444.26499999},{"pid":0,"ph":"X","args":{},"dur":1066.805999994278,"name":"DocumentSymbols","tid":250490,"ts":298711798.24199998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"detail":"void ()","kind":12,"name":"init_sdb","range":{"end":{"character":15,"line":8},"start":{"character":0,"line":8}},"selectionRange":{"end":{"character":13,"line":8},"start":{"character":5,"line":8}}},{"detail":"void ()","kind":12,"name":"init_rand","range":{"end":{"character":16,"line":9},"start":{"character":0,"line":9}},"selectionRange":{"end":{"character":14,"line":9},"start":{"character":5,"line":9}}},{"detail":"void (const char *)","kind":12,"name":"init_log","range":{"end":{"character":35,"line":10},"start":{"character":0,"line":10}},"selectionRange":{"end":{"character":13,"line":10},"start":{"character":5,"line":10}}},{"detail":"void ()","kind":12,"name":"init_mem","range":{"end":{"character":15,"line":11},"start":{"character":0,"line":11}},"selectionRange":{"end":{"character":13,"line":11},"start":{"character":5,"line":11}}},{"detail":"void (char *, long, int)","kind":12,"name":"init_difftest","range":{"end":{"character":62,"line":12},"start":{"character":0,"line":12}},"selectionRange":{"end":{"character":18,"line":12},"start":{"character":5,"line":12}}},{"detail":"void ()","kind":12,"name":"welcome","range":{"end":{"character":1,"line":25},"start":{"character":0,"line":16}},"selectionRange":{"end":{"character":12,"line":16},"start":{"character":5,"line":16}}},{"detail":"void ()","kind":12,"name":"sdb_set_batch_mode","range":{"end":{"character":25,"line":29},"start":{"character":0,"line":29}},"selectionRange":{"end":{"character":23,"line":29},"start":{"character":5,"line":29}}},{"detail":"char *","kind":13,"name":"log_file","range":{"end":{"character":28,"line":31},"start":{"character":0,"line":31}},"selectionRange":{"end":{"character":21,"line":31},"start":{"character":13,"line":31}}},{"detail":"char *","kind":13,"name":"diff_so_file","range":{"end":{"character":32,"line":32},"start":{"character":0,"line":32}},"selectionRange":{"end":{"character":25,"line":32},"start":{"character":13,"line":32}}},{"detail":"char *","kind":13,"name":"img_file","range":{"end":{"character":28,"line":33},"start":{"character":0,"line":33}},"selectionRange":{"end":{"character":21,"line":33},"start":{"character":13,"line":33}}},{"detail":"FILE *","kind":13,"name":"elf_file","range":{"end":{"character":21,"line":34},"start":{"character":0,"line":34}},"selectionRange":{"end":{"character":14,"line":34},"start":{"character":6,"line":34}}},{"detail":"int","kind":13,"name":"difftest_port","range":{"end":{"character":31,"line":35},"start":{"character":0,"line":35}},"selectionRange":{"end":{"character":24,"line":35},"start":{"character":11,"line":35}}},{"detail":"void ()","kind":12,"name":"ftrace_init","range":{"end":{"character":25,"line":38},"start":{"character":0,"line":38}},"selectionRange":{"end":{"character":23,"line":38},"start":{"character":12,"line":38}}},{"detail":"int (int, char **)","kind":12,"name":"parse_args","range":{"end":{"character":1,"line":71},"start":{"character":0,"line":39}},"selectionRange":{"end":{"character":21,"line":39},"start":{"character":11,"line":39}}},{"detail":"long ()","kind":12,"name":"load_img","range":{"end":{"character":1,"line":95},"start":{"character":0,"line":76}},"selectionRange":{"end":{"character":20,"line":76},"start":{"character":12,"line":76}}},{"detail":"const uint32_t[4]","kind":13,"name":"img","range":{"end":{"character":1,"line":102},"start":{"character":0,"line":97}},"selectionRange":{"end":{"character":25,"line":97},"start":{"character":22,"line":97}}},{"detail":"void ()","kind":12,"name":"restart","range":{"end":{"character":1,"line":110},"start":{"character":0,"line":104}},"selectionRange":{"end":{"character":19,"line":104},"start":{"character":12,"line":104}}},{"detail":"void ()","kind":12,"name":"init_isa","range":{"end":{"character":1,"line":121},"start":{"character":0,"line":115}},"selectionRange":{"end":{"character":13,"line":115},"start":{"character":5,"line":115}}},{"detail":"void (int, char **)","kind":12,"name":"init_monitor","range":{"end":{"character":1,"line":146},"start":{"character":0,"line":123}},"selectionRange":{"end":{"character":17,"line":123},"start":{"character":5,"line":123}}}]},"dur":120.91100001335144,"name":"textDocument/documentSymbol","tid":241390,"ts":298711618.65899998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":298713062.38499999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(65)"},"name":"Log","tid":241390,"ts":299012719.28799999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.246999979019165,"name":"Queued:InlayHints","tid":241390,"ts":299012769.83399999},{"pid":0,"ph":"s","cat":"mock_cat","id":75,"name":"Context crosses threads","tid":241390,"ts":299012696.76499999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":75,"name":"Context crosses threads","tid":250490,"ts":299012810.89300001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.018999993801117,"name":"getConfig","tid":250490,"ts":299012816.23299998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(65) 0 ms"},"name":"Log","tid":250490,"ts":299013589.329},{"pid":0,"ph":"X","args":{},"dur":1151.0670000314713,"name":"InlayHints","tid":250490,"ts":299012803.89899999},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":17,"line":115},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":9,"line":22}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":41}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":41}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":41}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":41}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":41}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":42}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":42}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":42}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":42}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":42}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":43}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":43}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":43}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":43}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":43}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":44}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":44}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":44}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":44}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":44}},{"label":[{"value":"[4]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":45}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":45}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":45}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":45}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":45}},{"label":[{"value":"[5]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":46}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":46}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":46}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":46}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":48,"line":46}},{"kind":2,"label":[{"value":"shortopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":41,"line":49}},{"kind":2,"label":[{"value":"longopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":56,"line":49}},{"kind":2,"label":[{"value":"longind:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":63,"line":49}},{"kind":2,"label":[{"value":"s:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":25,"line":53}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":33,"line":53}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":35,"line":55}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":42,"line":55}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":58}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":59}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":60}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":61}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":62}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":63}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":64}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":65}},{"kind":2,"label":[{"value":"status:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":66}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":81}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":32,"line":81}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":84}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":84}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":84}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":23,"line":85}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":89}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":89}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":89}},{"kind":2,"label":[{"value":"ptr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":90}},{"kind":2,"label":[{"value":"paddr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":36,"line":90}},{"kind":2,"label":[{"value":"n:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":57,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":60,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":12,"line":93}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":98}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":99}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":100}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":101}}]},"dur":84.601000010967255,"name":"textDocument/inlayHint","tid":241390,"ts":299012696.76499999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":299014135.73100001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentLink(66)"},"name":"Log","tid":241390,"ts":299214475.93599999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.0059999823570251,"name":"Queued:DocumentLinks","tid":241390,"ts":299214526.833},{"pid":0,"ph":"s","cat":"mock_cat","id":76,"name":"Context crosses threads","tid":241390,"ts":299214457.361},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":76,"name":"Context crosses threads","tid":250490,"ts":299214570.92799997},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":13.736000001430511,"name":"getConfig","tid":250490,"ts":299214578.01099998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentLink(66) 0 ms"},"name":"Log","tid":250490,"ts":299214707.61799997},{"pid":0,"ph":"X","args":{},"dur":223.74700003862381,"name":"DocumentLinks","tid":250490,"ts":299214561.21899998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"range":{"end":{"character":19,"line":0},"start":{"character":9,"line":0}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/common.h"},{"range":{"end":{"character":25,"line":3},"start":{"character":9,"line":3}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/memory/paddr.h"},{"range":{"end":{"character":19,"line":4},"start":{"character":9,"line":4}},"target":"file:///usr/include/getopt.h"},{"range":{"end":{"character":20,"line":5},"start":{"character":9,"line":5}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/cpu/cpu.h"},{"range":{"end":{"character":25,"line":6},"start":{"character":9,"line":6}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/cpu/difftest.h"},{"range":{"end":{"character":19,"line":27},"start":{"character":9,"line":27}},"target":"file:///usr/include/getopt.h"}]},"dur":80.824000000953674,"name":"textDocument/documentLink","tid":241390,"ts":299214457.361},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":299214859.49800003},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/didSave"},"name":"Log","tid":241390,"ts":299225533.972},{"pid":0,"ph":"i","args":{"Message":"File version went from 2 to 2"},"name":"Log","tid":241390,"ts":299225582.09399998},{"pid":0,"ph":"i","args":{"Message":"File version went from 3 to 3"},"name":"Log","tid":241390,"ts":299225611.63},{"pid":0,"ph":"s","cat":"mock_cat","id":77,"name":"Context crosses threads","tid":241390,"ts":299225517.35000002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":77,"name":"Context crosses threads","tid":250490,"ts":299225630.16500002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":299225645.03399998},{"pid":0,"ph":"s","cat":"mock_cat","id":78,"name":"Context crosses threads","tid":241390,"ts":299225517.35000002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":78,"name":"Context crosses threads","tid":250166,"ts":299225669.51999998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":299225685.18000001},{"pid":0,"ph":"X","args":{"next_request":"Update","sleep_ms":49},"dur":50062.430000007153,"name":"Debounce","tid":250490,"ts":299225617.33099997},{"pid":0,"ph":"X","args":{"next_request":"Update","sleep_ms":49},"dur":50051.600000023842,"name":"Debounce","tid":250166,"ts":299225652.73799998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.2149999737739563,"name":"Queued:Update","tid":241390,"ts":299225601.861},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":10.719999969005585,"name":"Queued:Update","tid":241390,"ts":299225624.72500002},{"pid":0,"ph":"s","cat":"mock_cat","id":79,"name":"Context crosses threads","tid":241390,"ts":299225517.35000002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":79,"name":"Context crosses threads","tid":250490,"ts":299275789.71100003},{"pid":0,"ph":"s","cat":"mock_cat","id":80,"name":"Context crosses threads","tid":241390,"ts":299225517.35000002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":80,"name":"Context crosses threads","tid":250166,"ts":299275805.02999997},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":43.792999982833862,"name":"getConfig","tid":250490,"ts":299275798.91900003},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":9.7890000343322754,"name":"getConfig","tid":250490,"ts":299275861.40799999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":83.719999969005585,"name":"getConfig","tid":250166,"ts":299275810.52100003},{"pid":0,"ph":"i","args":{"Message":"Failed to find compilation database for /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"name":"Log","tid":250490,"ts":299275911.26300001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":9.4979999661445618,"name":"getConfig","tid":250166,"ts":299275910.16100001},{"pid":0,"ph":"i","args":{"Message":"Failed to find compilation database for /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"name":"Log","tid":250166,"ts":299275938.014},{"pid":0,"ph":"X","args":{},"dur":143.41399997472763,"name":"AdjustCompileFlags","tid":250490,"ts":299275972.80000001},{"pid":0,"ph":"i","args":{"Message":"ASTWorker building file /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp version 2 with command clangd fallback\n[/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor]\n/usr/lib/llvm-14/bin/clang -I/home/<USER>/Desktop/ysyx-workbench/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/nemu/src/isa/riscv32/include/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/build/obj_dir/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/klib/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/src/platform/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include -I/home/<USER>/Desktop/ysyx-workbench/nvboard/include/ -I/usr/include/SDL2/ -I/usr/share/verilator/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/csrc/include -include=/headers/file.h -I/other/headers -resource-dir=/home/<USER>/.config/Code/User/globalStorage/llvm-vs-code-extensions.vscode-clangd/install/19.1.2/clangd_19.1.2/lib/clang/19 -- /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"name":"Log","tid":250490,"ts":299276151.602},{"pid":0,"ph":"X","args":{},"dur":47.660999953746796,"name":"AdjustCompileFlags","tid":250166,"ts":299276137.78500003},{"pid":0,"ph":"i","args":{"Message":"ASTWorker building file /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp version 3 with command clangd fallback\n[/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils]\n/usr/lib/llvm-14/bin/clang -I/home/<USER>/Desktop/ysyx-workbench/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/nemu/src/isa/riscv32/include/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/build/obj_dir/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/klib/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/src/platform/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include -I/home/<USER>/Desktop/ysyx-workbench/nvboard/include/ -I/usr/include/SDL2/ -I/usr/share/verilator/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/csrc/include -include=/headers/file.h -I/other/headers -resource-dir=/home/<USER>/.config/Code/User/globalStorage/llvm-vs-code-extensions.vscode-clangd/install/19.1.2/clangd_19.1.2/lib/clang/19 -- /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"name":"Log","tid":250166,"ts":299276267.97399998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":299277647.65700001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":299277647.81699997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250491,"ts":299277751.15499997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250167,"ts":299277769.82999998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250167,"ts":299278302.10600001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":6.1209999918937683,"name":"getConfig","tid":250166,"ts":299278340.079},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250491,"ts":299278365.75800002},{"pid":0,"ph":"X","args":{},"dur":25.869000017642975,"name":"Build AST","tid":250166,"ts":299278338.01499999},{"pid":0,"ph":"X","args":{},"dur":1842.4670000076294,"name":"Update","tid":250166,"ts":299275783.62900001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":299278390.45499998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":7.234000027179718,"name":"getConfig","tid":250490,"ts":299278398.51999998},{"pid":0,"ph":"X","args":{},"dur":29.135999977588654,"name":"Build AST","tid":250490,"ts":299278396.236},{"pid":0,"ph":"X","args":{},"dur":1849.6299999952316,"name":"Update","tid":250490,"ts":299275777.708},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}}},"dur":122.59399998188019,"name":"textDocument/didSave","tid":241390,"ts":299225517.35000002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":299278478.70300001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(67)"},"name":"Log","tid":241390,"ts":299309340.01599997},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.1240000128746033,"name":"Queued:codeAction","tid":241390,"ts":299309418.90700001},{"pid":0,"ph":"s","cat":"mock_cat","id":81,"name":"Context crosses threads","tid":241390,"ts":299309307.25400001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":81,"name":"Context crosses threads","tid":250490,"ts":299309478.58099997},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":22.773000001907349,"name":"getConfig","tid":250490,"ts":299309486.33600003},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(67) 0 ms"},"name":"Log","tid":250490,"ts":299309675.06599998},{"pid":0,"ph":"X","args":{},"dur":254.90600001811981,"name":"codeAction","tid":250490,"ts":299309469.65399998},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":28,"line":31},"start":{"character":28,"line":31}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":28,"line":31},"start":{"character":28,"line":31}},"tweakID":"ExpandMacro"}],"command":"clangd.applyTweak","title":"Expand macro 'NULL'"},"kind":"refactor","title":"Expand macro 'NULL'"}]},"dur":123.31499999761581,"name":"textDocument/codeAction","tid":241390,"ts":299309307.25400001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":299309758.245},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(68)"},"name":"Log","tid":241390,"ts":299538315.66600001},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.7150000333786011,"name":"Queued:SemanticHighlights","tid":241390,"ts":299538353.42799997},{"pid":0,"ph":"s","cat":"mock_cat","id":82,"name":"Context crosses threads","tid":241390,"ts":299538303.20200002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":82,"name":"Context crosses threads","tid":250490,"ts":299538393.12300003},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.91100001335144,"name":"getConfig","tid":250490,"ts":299538398.764},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(68) 0 ms"},"name":"Log","tid":250490,"ts":299538752.88999999},{"pid":0,"ph":"X","args":{},"dur":437.59499996900558,"name":"SemanticHighlights","tid":250490,"ts":299538385.55900002},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"data":[8,5,8,3,131073,1,5,9,3,131073,1,5,8,3,131073,0,21,8,2,16403,1,5,8,3,131073,1,5,13,3,131073,0,20,11,2,16387,0,18,8,2,16387,0,14,4,2,16387,4,5,7,3,131075,2,2,5,19,131072,0,6,12,19,131072,0,14,3,19,131072,3,2,3,19,131072,1,2,6,3,131584,2,2,6,19,131072,5,5,18,3,131073,2,13,8,0,65539,0,11,4,19,131072,1,13,12,0,65539,0,15,4,19,131072,1,13,8,0,65539,0,11,4,19,131072,1,0,4,8,66048,0,6,8,0,131075,0,11,4,19,131072,1,11,13,0,65539,3,12,11,3,131073,1,11,10,3,65539,0,15,4,2,16387,0,12,4,2,16387,1,17,6,8,131584,0,7,5,1,16403,1,22,11,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,41,4,19,131072,2,8,1,1,16387,1,13,1,1,16384,0,2,1,21,0,0,2,11,3,131584,0,12,4,2,16384,0,6,4,2,16384,0,21,5,1,16400,0,7,4,19,131072,0,7,2,21,0,0,3,1,21,0,1,14,1,1,16384,1,18,18,3,131072,1,18,8,0,65536,0,9,1,21,0,0,2,6,0,131584,1,18,6,3,131584,0,7,6,0,131584,0,14,1,21,0,0,1,13,0,65536,1,18,12,0,65536,0,13,1,21,0,0,2,6,0,131584,1,18,8,0,131072,0,9,1,21,0,0,2,5,3,131584,0,6,6,0,131584,0,13,11,3,131072,1,16,8,0,65536,0,9,1,21,0,0,2,6,0,131584,2,10,6,3,131584,0,49,4,2,16384,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,4,3,131584,10,12,8,3,65539,1,8,8,0,65536,0,9,2,21,0,0,3,4,19,131072,1,6,3,19,131072,3,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,7,8,0,65536,1,4,6,19,131072,0,8,2,1,16384,0,25,8,0,65536,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,9,4,1,16387,0,7,5,3,131584,0,7,2,1,18432,2,4,3,19,131072,0,36,8,0,65536,0,10,4,1,16384,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,8,3,1,16387,0,6,5,3,131584,0,7,13,3,131072,0,15,12,19,131072,0,15,4,1,16384,0,9,2,1,18432,1,4,6,19,131072,0,8,3,1,16384,0,4,2,21,0,2,4,6,3,131584,0,8,2,1,18432,1,11,4,1,16384,3,13,8,18,66048,0,9,3,0,65555,7,12,7,3,65539,2,11,12,19,131072,9,5,8,3,131075,2,2,6,3,131584,0,7,13,3,131072,0,14,12,19,131072,0,15,3,0,65552,0,12,3,0,65552,3,2,7,3,65536,3,5,12,3,131075,0,18,4,2,16387,0,12,4,2,16387,3,4,10,3,65536,0,12,4,2,16384,0,6,4,2,18432,2,4,9,3,131072,3,4,8,3,131072,0,10,8,0,65536,3,4,8,3,131072,2,4,8,3,131072,2,9,8,1,16387,0,11,8,3,65536,7,4,7,3,131072],"resultId":"4"}},"dur":56.62799996137619,"name":"textDocument/semanticTokens/full","tid":241390,"ts":299538303.20200002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":299538916.542},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(69)"},"name":"Log","tid":241390,"ts":299737310.89300001},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250613},{"pid":0,"ph":"X","args":{},"dur":12.764999985694885,"name":"WaitForFreeSemaphoreSlot","tid":250613,"ts":299737419.921},{"pid":0,"ph":"s","cat":"mock_cat","id":83,"name":"Context crosses threads","tid":241390,"ts":299737297.53799999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":83,"name":"Context crosses threads","tid":250613,"ts":299737448.08499998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":16.601999998092651,"name":"getConfig","tid":250613,"ts":299737442.384},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(69) 0 ms"},"name":"Log","tid":250613,"ts":299737837.41799998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endCharacter":67,"endLine":19,"kind":"region","startCharacter":8,"startLine":18},{"endCharacter":66,"endLine":19,"kind":"region","startCharacter":26,"startLine":18},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":102.11500000953674,"name":"textDocument/foldingRange","tid":241390,"ts":299737297.53799999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(70)"},"name":"Log","tid":241390,"ts":299887474.68000001},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":250614},{"pid":0,"ph":"X","args":{},"dur":13.055000007152557,"name":"WaitForFreeSemaphoreSlot","tid":250614,"ts":299887575.59299999},{"pid":0,"ph":"s","cat":"mock_cat","id":84,"name":"Context crosses threads","tid":241390,"ts":299887461.10399997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":84,"name":"Context crosses threads","tid":250614,"ts":299887603.95700002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":16.73199999332428,"name":"getConfig","tid":250614,"ts":299887598.35600001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(70) 0 ms"},"name":"Log","tid":250614,"ts":299887986.43699998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endCharacter":67,"endLine":19,"kind":"region","startCharacter":8,"startLine":18},{"endCharacter":66,"endLine":19,"kind":"region","startCharacter":26,"startLine":18},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":94.39000004529953,"name":"textDocument/foldingRange","tid":241390,"ts":299887461.10399997},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(71)"},"name":"Log","tid":241390,"ts":299893043.19800001},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.5770000219345093,"name":"Queued:InlayHints","tid":241390,"ts":299893092.162},{"pid":0,"ph":"s","cat":"mock_cat","id":85,"name":"Context crosses threads","tid":241390,"ts":299893021.20599997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":85,"name":"Context crosses threads","tid":250490,"ts":299893132.88},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":12.853999972343445,"name":"getConfig","tid":250490,"ts":299893137.60900003},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(71) 1 ms"},"name":"Log","tid":250490,"ts":299894054.53899997},{"pid":0,"ph":"X","args":{},"dur":1339.1759999990463,"name":"InlayHints","tid":250490,"ts":299893125.16500002},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":17,"line":115},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":9,"line":22}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":41}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":41}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":41}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":41}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":41}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":42}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":42}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":42}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":42}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":42}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":43}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":43}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":43}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":43}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":43}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":44}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":44}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":44}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":44}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":44}},{"label":[{"value":"[4]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":45}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":45}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":45}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":45}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":45}},{"label":[{"value":"[5]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":46}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":46}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":46}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":46}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":48,"line":46}},{"kind":2,"label":[{"value":"shortopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":41,"line":49}},{"kind":2,"label":[{"value":"longopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":56,"line":49}},{"kind":2,"label":[{"value":"longind:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":63,"line":49}},{"kind":2,"label":[{"value":"s:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":25,"line":53}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":33,"line":53}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":35,"line":55}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":42,"line":55}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":58}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":59}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":60}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":61}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":62}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":63}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":64}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":65}},{"kind":2,"label":[{"value":"status:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":66}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":81}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":32,"line":81}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":84}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":84}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":84}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":23,"line":85}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":89}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":89}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":89}},{"kind":2,"label":[{"value":"ptr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":90}},{"kind":2,"label":[{"value":"paddr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":36,"line":90}},{"kind":2,"label":[{"value":"n:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":57,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":60,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":12,"line":93}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":98}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":99}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":100}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":101}}]},"dur":81.757000029087067,"name":"textDocument/inlayHint","tid":241390,"ts":299893021.20599997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":299894690.79299998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(72)"},"name":"Log","tid":241390,"ts":299949449.77700001},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.0740000009536743,"name":"Queued:codeAction","tid":241390,"ts":299949488.62099999},{"pid":0,"ph":"s","cat":"mock_cat","id":86,"name":"Context crosses threads","tid":241390,"ts":299949431.92299998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":86,"name":"Context crosses threads","tid":250490,"ts":299949549.99900001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.25,"name":"getConfig","tid":250490,"ts":299949555.01800001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(72) 0 ms"},"name":"Log","tid":250490,"ts":299949719.04100001},{"pid":0,"ph":"X","args":{},"dur":198.87000000476837,"name":"codeAction","tid":250490,"ts":299949544.07700002},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":0,"line":25},"start":{"character":0,"line":25}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":65.114000022411346,"name":"textDocument/codeAction","tid":241390,"ts":299949431.92299998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":299949764.68800002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(73)"},"name":"Log","tid":241390,"ts":388032389.33200002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.5649999976158142,"name":"Queued:InlayHints","tid":241390,"ts":388032432.505},{"pid":0,"ph":"s","cat":"mock_cat","id":87,"name":"Context crosses threads","tid":241390,"ts":388032368.08200002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":87,"name":"Context crosses threads","tid":250490,"ts":388032475.54699999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":169.44300001859665,"name":"getConfig","tid":250490,"ts":388032481.15799999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(73) 1 ms"},"name":"Log","tid":250490,"ts":388033404.91100001},{"pid":0,"ph":"X","args":{},"dur":1316.0810000300407,"name":"InlayHints","tid":250490,"ts":388032468.17299998},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":56,"line":117},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":9,"line":22}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":41}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":41}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":41}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":41}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":41}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":42}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":42}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":42}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":42}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":42}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":43}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":43}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":43}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":43}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":43}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":44}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":44}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":44}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":44}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":44}},{"label":[{"value":"[4]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":45}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":45}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":45}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":45}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":45}},{"label":[{"value":"[5]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":46}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":46}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":46}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":46}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":48,"line":46}},{"kind":2,"label":[{"value":"shortopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":41,"line":49}},{"kind":2,"label":[{"value":"longopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":56,"line":49}},{"kind":2,"label":[{"value":"longind:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":63,"line":49}},{"kind":2,"label":[{"value":"s:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":25,"line":53}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":33,"line":53}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":35,"line":55}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":42,"line":55}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":58}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":59}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":60}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":61}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":62}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":63}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":64}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":65}},{"kind":2,"label":[{"value":"status:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":66}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":81}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":32,"line":81}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":84}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":84}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":84}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":23,"line":85}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":89}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":89}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":89}},{"kind":2,"label":[{"value":"ptr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":90}},{"kind":2,"label":[{"value":"paddr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":36,"line":90}},{"kind":2,"label":[{"value":"n:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":57,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":60,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":12,"line":93}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":98}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":99}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":100}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":101}},{"kind":2,"label":[{"value":"dest:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":9,"line":117}},{"kind":2,"label":[{"value":"paddr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":23,"line":117}},{"kind":2,"label":[{"value":"src:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":38,"line":117}},{"kind":2,"label":[{"value":"n:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":43,"line":117}}]},"dur":72.677999973297119,"name":"textDocument/inlayHint","tid":241390,"ts":388032368.08200002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":388034124.824},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(74)"},"name":"Log","tid":241390,"ts":388973949.66000003},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.2739999890327454,"name":"Queued:codeAction","tid":241390,"ts":388973994.53600001},{"pid":0,"ph":"s","cat":"mock_cat","id":88,"name":"Context crosses threads","tid":241390,"ts":388973914.02200001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":88,"name":"Context crosses threads","tid":250166,"ts":388974052.94700003},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":50.28600001335144,"name":"getConfig","tid":250166,"ts":388974059.31999999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(74) 0 ms"},"name":"Log","tid":250166,"ts":388974166.69400001},{"pid":0,"ph":"X","args":{},"dur":157.18999999761581,"name":"codeAction","tid":250166,"ts":388974043.60000002},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":0,"line":0},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[]},"dur":88.939999997615814,"name":"textDocument/codeAction","tid":241390,"ts":388973914.02200001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":388974239.31300002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentLink(75)"},"name":"Log","tid":241390,"ts":389047366.27499998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.3140000104904175,"name":"Queued:DocumentLinks","tid":241390,"ts":389047405.98100001},{"pid":0,"ph":"s","cat":"mock_cat","id":89,"name":"Context crosses threads","tid":241390,"ts":389047352.18800002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":89,"name":"Context crosses threads","tid":250166,"ts":389047453.74199998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":13.726000010967255,"name":"getConfig","tid":250166,"ts":389047460.014},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentLink(75) 0 ms"},"name":"Log","tid":250166,"ts":389047526.13},{"pid":0,"ph":"X","args":{},"dur":143.29299998283386,"name":"DocumentLinks","tid":250166,"ts":389047443.99400002},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"range":{"end":{"character":19,"line":15},"start":{"character":9,"line":15}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/common.h"}]},"dur":60.084999978542328,"name":"textDocument/documentLink","tid":241390,"ts":389047352.18800002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":389047627.634},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(76)"},"name":"Log","tid":241390,"ts":389067777.66299999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.7149999737739563,"name":"Queued:InlayHints","tid":241390,"ts":389067813.95200002},{"pid":0,"ph":"s","cat":"mock_cat","id":90,"name":"Context crosses threads","tid":241390,"ts":389067759.93900001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":90,"name":"Context crosses threads","tid":250166,"ts":389067875.56},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":9.8080000281333923,"name":"getConfig","tid":250166,"ts":389067880.26899999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(76) 0 ms"},"name":"Log","tid":250166,"ts":389067962.42500001},{"pid":0,"ph":"X","args":{},"dur":146.11899995803833,"name":"InlayHints","tid":250166,"ts":389067868.27600002},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":0,"line":37},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":23}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":31,"line":23}}]},"dur":61.818000018596649,"name":"textDocument/inlayHint","tid":241390,"ts":389067759.93900001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":389068041.86699998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(77)"},"name":"Log","tid":241390,"ts":389171479.22100002},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":251797},{"pid":0,"ph":"X","args":{},"dur":13.666000008583069,"name":"WaitForFreeSemaphoreSlot","tid":251797,"ts":389171638.02399999},{"pid":0,"ph":"s","cat":"mock_cat","id":91,"name":"Context crosses threads","tid":241390,"ts":389171441.37800002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":91,"name":"Context crosses threads","tid":251797,"ts":389171673.68199998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":22.803000032901764,"name":"getConfig","tid":251797,"ts":389171665.74699998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(77) 0 ms"},"name":"Log","tid":251797,"ts":389171933.63800001},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":167.04899996519089,"name":"textDocument/foldingRange","tid":241390,"ts":389171441.37800002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(78)"},"name":"Log","tid":241390,"ts":389232141.26200002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":5.5,"name":"Queued:codeAction","tid":241390,"ts":389232244.49900001},{"pid":0,"ph":"s","cat":"mock_cat","id":92,"name":"Context crosses threads","tid":241390,"ts":389232069.34500003},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":92,"name":"Context crosses threads","tid":250166,"ts":389232332.43699998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":26.400000035762787,"name":"getConfig","tid":250166,"ts":389232338.78899997},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(78) 0 ms"},"name":"Log","tid":250166,"ts":389232489.24599999},{"pid":0,"ph":"X","args":{},"dur":211.18299996852875,"name":"codeAction","tid":250166,"ts":389232319.833},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":5,"line":27},"start":{"character":5,"line":27}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[]},"dur":194.91200000047684,"name":"textDocument/codeAction","tid":241390,"ts":389232069.34500003},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":389232574.86900002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(79)"},"name":"Log","tid":241390,"ts":389268867.25800002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.0740000009536743,"name":"Queued:SemanticHighlights","tid":241390,"ts":389268905.22000003},{"pid":0,"ph":"s","cat":"mock_cat","id":93,"name":"Context crosses threads","tid":241390,"ts":389268851.60799998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":93,"name":"Context crosses threads","tid":250166,"ts":389268945.55699998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":11.442000031471252,"name":"getConfig","tid":250166,"ts":389268950.61699998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(79) 0 ms"},"name":"Log","tid":250166,"ts":389269078.421},{"pid":0,"ph":"X","args":{},"dur":178.31000000238419,"name":"SemanticHighlights","tid":250166,"ts":389268938.62400001},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":{"data":[17,7,8,18,66048,0,9,15,0,131073,1,0,4,8,66048,0,6,6,0,131075,0,9,4,19,131072,2,5,8,3,131075,0,21,8,2,16403,1,2,6,0,131072,0,7,1,21,0,0,2,6,19,131072,1,6,8,2,16400,0,9,2,21,0,0,3,4,19,131072,1,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,6,8,2,16400,1,4,6,19,131072,0,7,2,1,16384,0,25,8,2,16400,1,4,6,0,131072,0,7,1,21,0,0,2,2,1,16384,5,5,10,3,131075,1,7,17,19,131072,3,11,6,19,131072,0,7,12,19,131072,0,15,15,0,131072,0,16,2,21,0,0,3,16,19,131072],"resultId":"6"}},"dur":61.137000024318695,"name":"textDocument/semanticTokens/full","tid":241390,"ts":389268851.60799998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":389269144.58700001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(80)"},"name":"Log","tid":241390,"ts":389274471.05400002},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":251815},{"pid":0,"ph":"X","args":{},"dur":10.279999971389771,"name":"WaitForFreeSemaphoreSlot","tid":251815,"ts":389274580.95300001},{"pid":0,"ph":"s","cat":"mock_cat","id":94,"name":"Context crosses threads","tid":241390,"ts":389274461.56599998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":94,"name":"Context crosses threads","tid":251815,"ts":389274604.26800001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":13.626000046730042,"name":"getConfig","tid":251815,"ts":389274599.34899998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(80) 0 ms"},"name":"Log","tid":251815,"ts":389274738.153},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":103.49700003862381,"name":"textDocument/foldingRange","tid":241390,"ts":389274461.56599998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(81)"},"name":"Log","tid":241390,"ts":389430384.75400001},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.6649999618530273,"name":"Queued:SemanticHighlights","tid":241390,"ts":389430425.95300001},{"pid":0,"ph":"s","cat":"mock_cat","id":95,"name":"Context crosses threads","tid":241390,"ts":389430370.23699999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":95,"name":"Context crosses threads","tid":250166,"ts":389430465.92900002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":10.148999989032745,"name":"getConfig","tid":250166,"ts":389430470.34799999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(81) 0 ms"},"name":"Log","tid":250166,"ts":389430588.39300001},{"pid":0,"ph":"X","args":{},"dur":162.70099997520447,"name":"SemanticHighlights","tid":250166,"ts":389430459.597},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":{"data":[17,7,8,18,66048,0,9,15,0,131073,1,0,4,8,66048,0,6,6,0,131075,0,9,4,19,131072,2,5,8,3,131075,0,21,8,2,16403,1,2,6,0,131072,0,7,1,21,0,0,2,6,19,131072,1,6,8,2,16400,0,9,2,21,0,0,3,4,19,131072,1,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,6,8,2,16400,1,4,6,19,131072,0,7,2,1,16384,0,25,8,2,16400,1,4,6,0,131072,0,7,1,21,0,0,2,2,1,16384,5,5,10,3,131075,1,7,17,19,131072,3,11,6,19,131072,0,7,12,19,131072,0,15,15,0,131072,0,16,2,21,0,0,3,16,19,131072],"resultId":"7"}},"dur":63.319999992847443,"name":"textDocument/semanticTokens/full","tid":241390,"ts":389430370.23699999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":389430657.33399999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(82)"},"name":"Log","tid":241390,"ts":389483610.21200001},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.3650000095367432,"name":"Queued:InlayHints","tid":241390,"ts":389483665.92799997},{"pid":0,"ph":"s","cat":"mock_cat","id":96,"name":"Context crosses threads","tid":241390,"ts":389483590.29400003},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":96,"name":"Context crosses threads","tid":250166,"ts":389483714.76099998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":10.930000007152557,"name":"getConfig","tid":250166,"ts":389483719.56099999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(82) 0 ms"},"name":"Log","tid":250166,"ts":389483807.55900002},{"pid":0,"ph":"X","args":{},"dur":191.65600001811981,"name":"InlayHints","tid":250166,"ts":389483708.39899999},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":0,"line":37},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":23}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":31,"line":23}}]},"dur":83.258999943733215,"name":"textDocument/inlayHint","tid":241390,"ts":389483590.29400003},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":389483927.528},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(83)"},"name":"Log","tid":241390,"ts":389630063.77700001},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":251827},{"pid":0,"ph":"X","args":{},"dur":15.639999985694885,"name":"WaitForFreeSemaphoreSlot","tid":251827,"ts":389630225.01499999},{"pid":0,"ph":"s","cat":"mock_cat","id":97,"name":"Context crosses threads","tid":241390,"ts":389630026.70599997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":97,"name":"Context crosses threads","tid":251827,"ts":389630259.25},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":21.831999957561493,"name":"getConfig","tid":251827,"ts":389630252.38700002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(83) 0 ms"},"name":"Log","tid":251827,"ts":389630448},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":161.50900000333786,"name":"textDocument/foldingRange","tid":241390,"ts":389630026.70599997},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(84)"},"name":"Log","tid":241390,"ts":389682543.10100001},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.9629999995231628,"name":"Queued:codeAction","tid":241390,"ts":389682581.83499998},{"pid":0,"ph":"s","cat":"mock_cat","id":98,"name":"Context crosses threads","tid":241390,"ts":389682524.03399998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":98,"name":"Context crosses threads","tid":250166,"ts":389682643.10100001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":13.896000027656555,"name":"getConfig","tid":250166,"ts":389682650.065},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(84) 0 ms"},"name":"Log","tid":250166,"ts":389682730.26800001},{"pid":0,"ph":"X","args":{},"dur":134.81699997186661,"name":"codeAction","tid":250166,"ts":389682631.29900002},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":5,"line":27},"start":{"character":5,"line":27}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[]},"dur":66.517000019550323,"name":"textDocument/codeAction","tid":241390,"ts":389682524.03399998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":389682805.89200002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(85)"},"name":"Log","tid":241390,"ts":389784751.12800002},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":251828},{"pid":0,"ph":"X","args":{},"dur":42.882000029087067,"name":"WaitForFreeSemaphoreSlot","tid":251828,"ts":389784865.21499997},{"pid":0,"ph":"s","cat":"mock_cat","id":99,"name":"Context crosses threads","tid":241390,"ts":389784734.75599998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":99,"name":"Context crosses threads","tid":251828,"ts":389784923.08600003},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":16.091000020503998,"name":"getConfig","tid":251828,"ts":389784917.796},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(85) 0 ms"},"name":"Log","tid":251828,"ts":389785055.46899998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"}},"Reply":[{"endLine":27,"kind":"region","startCharacter":37,"startLine":20},{"endCharacter":2,"endLine":25,"kind":"region","startCharacter":25,"startLine":22},{"endLine":35,"kind":"region","startCharacter":19,"startLine":30},{"endCharacter":86,"endLine":12,"kind":"comment","startCharacter":2,"startLine":0}]},"dur":109.28900003433228,"name":"textDocument/foldingRange","tid":241390,"ts":389784734.75599998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(86)"},"name":"Log","tid":241390,"ts":577871706.53799999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.1340000629425049,"name":"Queued:codeAction","tid":241390,"ts":577871751.05299997},{"pid":0,"ph":"s","cat":"mock_cat","id":100,"name":"Context crosses threads","tid":241390,"ts":577871648.13600004},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":100,"name":"Context crosses threads","tid":250490,"ts":577871797.38100004},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":187.13699996471405,"name":"getConfig","tid":250490,"ts":577871804.15400004},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(86) 0 ms"},"name":"Log","tid":250490,"ts":577872037.06799996},{"pid":0,"ph":"X","args":{},"dur":275.2849999666214,"name":"codeAction","tid":250490,"ts":577871787.88300002},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[{"code":"1696","message":"#include errors detected. Please update your includePath. Squiggles are disabled for this translation unit (/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp).","range":{"end":{"character":19,"line":0},"start":{"character":0,"line":0}},"severity":1,"source":"C/C++"},{"code":"1696","message":"cannot open source file \"common.h\"","range":{"end":{"character":19,"line":0},"start":{"character":0,"line":0}},"severity":1,"source":"C/C++"}],"triggerKind":2},"range":{"end":{"character":0,"line":0},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":113.16600000858307,"name":"textDocument/codeAction","tid":241390,"ts":577871648.13600004},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":577872116.04900002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentLink(87)"},"name":"Log","tid":241390,"ts":577922676.48099995},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.2439999580383301,"name":"Queued:DocumentLinks","tid":241390,"ts":577922717.88999999},{"pid":0,"ph":"s","cat":"mock_cat","id":101,"name":"Context crosses threads","tid":241390,"ts":577922661.171},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":101,"name":"Context crosses threads","tid":250490,"ts":577922766.14199996},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":16.442000031471252,"name":"getConfig","tid":250490,"ts":577922772.28299999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentLink(87) 0 ms"},"name":"Log","tid":250490,"ts":577922935.42499995},{"pid":0,"ph":"X","args":{},"dur":317.72699999809265,"name":"DocumentLinks","tid":250490,"ts":577922758.07599998},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"range":{"end":{"character":19,"line":0},"start":{"character":9,"line":0}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/common.h"},{"range":{"end":{"character":25,"line":3},"start":{"character":9,"line":3}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/memory/paddr.h"},{"range":{"end":{"character":19,"line":4},"start":{"character":9,"line":4}},"target":"file:///usr/include/getopt.h"},{"range":{"end":{"character":20,"line":5},"start":{"character":9,"line":5}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/cpu/cpu.h"},{"range":{"end":{"character":25,"line":6},"start":{"character":9,"line":6}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/cpu/difftest.h"},{"range":{"end":{"character":19,"line":27},"start":{"character":9,"line":27}},"target":"file:///usr/include/getopt.h"}]},"dur":66.256999969482422,"name":"textDocument/documentLink","tid":241390,"ts":577922661.171},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":577923117.03100002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(88)"},"name":"Log","tid":241390,"ts":578043925.34899998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.2539999485015869,"name":"Queued:SemanticHighlights","tid":241390,"ts":578043961.64900005},{"pid":0,"ph":"s","cat":"mock_cat","id":102,"name":"Context crosses threads","tid":241390,"ts":578043913.50699997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":102,"name":"Context crosses threads","tid":250490,"ts":578043993.26900005},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":9.5989999771118164,"name":"getConfig","tid":250490,"ts":578043997.11600006},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(88) 0 ms"},"name":"Log","tid":250490,"ts":578044263.40499997},{"pid":0,"ph":"X","args":{},"dur":332.2039999961853,"name":"SemanticHighlights","tid":250490,"ts":578043987.47800004},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"data":[8,5,8,3,131073,1,5,9,3,131073,1,5,8,3,131073,0,21,8,2,16403,1,5,8,3,131073,1,5,13,3,131073,0,20,11,2,16387,0,18,8,2,16387,0,14,4,2,16387,4,5,7,3,131075,2,2,5,19,131072,0,6,12,19,131072,0,14,3,19,131072,3,2,3,19,131072,1,2,6,3,131584,2,2,6,19,131072,5,5,18,3,131073,2,13,8,0,65539,0,11,4,19,131072,1,13,12,0,65539,0,15,4,19,131072,1,13,8,0,65539,0,11,4,19,131072,1,0,4,8,66048,0,6,8,0,131075,0,11,4,19,131072,1,11,13,0,65539,3,12,11,3,131073,1,11,10,3,65539,0,15,4,2,16387,0,12,4,2,16387,1,17,6,8,131584,0,7,5,1,16403,1,22,11,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,41,4,19,131072,2,8,1,1,16387,1,13,1,1,16384,0,2,1,21,0,0,2,11,3,131584,0,12,4,2,16384,0,6,4,2,16384,0,21,5,1,16400,0,7,4,19,131072,0,7,2,21,0,0,3,1,21,0,1,14,1,1,16384,1,18,18,3,131072,1,18,8,0,65536,0,9,1,21,0,0,2,6,0,131584,1,18,6,3,131584,0,7,6,0,131584,0,14,1,21,0,0,1,13,0,65536,1,18,12,0,65536,0,13,1,21,0,0,2,6,0,131584,1,18,8,0,131072,0,9,1,21,0,0,2,5,3,131584,0,6,6,0,131584,0,13,11,3,131072,1,16,8,0,65536,0,9,1,21,0,0,2,6,0,131584,2,10,6,3,131584,0,49,4,2,16384,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,4,3,131584,10,12,8,3,65539,1,8,8,0,65536,0,9,2,21,0,0,3,4,19,131072,1,6,3,19,131072,3,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,7,8,0,65536,1,4,6,19,131072,0,8,2,1,16384,0,25,8,0,65536,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,9,4,1,16387,0,7,5,3,131584,0,7,2,1,18432,2,4,3,19,131072,0,36,8,0,65536,0,10,4,1,16384,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,8,3,1,16387,0,6,5,3,131584,0,7,13,3,131072,0,15,12,19,131072,0,15,4,1,16384,0,9,2,1,18432,1,4,6,19,131072,0,8,3,1,16384,0,4,2,21,0,2,4,6,3,131584,0,8,2,1,18432,1,11,4,1,16384,3,13,8,18,66048,0,9,3,0,65555,7,12,7,3,65539,2,11,12,19,131072,9,5,8,3,131075,2,2,6,3,131584,0,7,13,3,131072,0,14,12,19,131072,0,15,3,0,65552,0,12,3,0,65552,3,2,7,3,65536,3,5,12,3,131075,0,18,4,2,16387,0,12,4,2,16387,3,4,10,3,65536,0,12,4,2,16384,0,6,4,2,18432,2,4,9,3,131072,3,4,8,3,131072,0,10,8,0,65536,3,4,8,3,131072,2,4,8,3,131072,2,9,8,1,16387,0,11,8,3,65536,7,4,7,3,131072],"resultId":"5"}},"dur":54.203000068664551,"name":"textDocument/semanticTokens/full","tid":241390,"ts":578043913.50699997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":578044376.60099995},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(89)"},"name":"Log","tid":241390,"ts":578072449.32099998},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":254359},{"pid":0,"ph":"X","args":{},"dur":12.52400004863739,"name":"WaitForFreeSemaphoreSlot","tid":254359,"ts":578072543.17999995},{"pid":0,"ph":"s","cat":"mock_cat","id":103,"name":"Context crosses threads","tid":241390,"ts":578072437.43799996},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":103,"name":"Context crosses threads","tid":254359,"ts":578072574.44000006},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":22.342000007629395,"name":"getConfig","tid":254359,"ts":578072566.68499994},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(89) 0 ms"},"name":"Log","tid":254359,"ts":578073091.76699996},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endCharacter":67,"endLine":19,"kind":"region","startCharacter":8,"startLine":18},{"endCharacter":66,"endLine":19,"kind":"region","startCharacter":26,"startLine":18},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":84.38100004196167,"name":"textDocument/foldingRange","tid":241390,"ts":578072437.43799996},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(90)"},"name":"Log","tid":241390,"ts":578092475.79400003},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.2039999961853027,"name":"Queued:InlayHints","tid":241390,"ts":578092512.48399997},{"pid":0,"ph":"s","cat":"mock_cat","id":104,"name":"Context crosses threads","tid":241390,"ts":578092449.98500001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":104,"name":"Context crosses threads","tid":250490,"ts":578092548.80299997},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.759999990463257,"name":"getConfig","tid":250490,"ts":578092584.12100005},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(90) 1 ms"},"name":"Log","tid":250490,"ts":578093886.18599999},{"pid":0,"ph":"X","args":{},"dur":1703.1010000705719,"name":"InlayHints","tid":250490,"ts":578092542.45099998},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":3,"line":114},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":18}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":9,"line":22}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":41}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":41}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":41}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":41}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":41}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":42}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":42}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":42}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":42}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":42}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":43}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":43}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":43}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":43}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":43}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":44}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":44}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":44}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":44}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":44}},{"label":[{"value":"[4]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":45}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":45}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":45}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":45}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":45}},{"label":[{"value":"[5]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":46}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":46}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":46}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":46}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":48,"line":46}},{"kind":2,"label":[{"value":"shortopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":41,"line":49}},{"kind":2,"label":[{"value":"longopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":56,"line":49}},{"kind":2,"label":[{"value":"longind:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":63,"line":49}},{"kind":2,"label":[{"value":"s:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":25,"line":53}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":33,"line":53}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":35,"line":55}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":42,"line":55}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":58}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":59}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":60}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":61}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":62}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":63}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":64}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":65}},{"kind":2,"label":[{"value":"status:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":66}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":81}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":32,"line":81}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":84}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":84}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":84}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":23,"line":85}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":89}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":89}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":89}},{"kind":2,"label":[{"value":"ptr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":90}},{"kind":2,"label":[{"value":"paddr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":36,"line":90}},{"kind":2,"label":[{"value":"n:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":57,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":60,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":12,"line":93}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":98}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":99}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":100}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":101}}]},"dur":69.212000012397766,"name":"textDocument/inlayHint","tid":241390,"ts":578092449.98500001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":578094413.80299997},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(91)"},"name":"Log","tid":241390,"ts":578294451.39999998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.3140000104904175,"name":"Queued:codeAction","tid":241390,"ts":578294507.60800004},{"pid":0,"ph":"s","cat":"mock_cat","id":105,"name":"Context crosses threads","tid":241390,"ts":578294426.19200003},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":105,"name":"Context crosses threads","tid":250490,"ts":578294562.50199997},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":13.435000061988831,"name":"getConfig","tid":250490,"ts":578294568.454},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(91) 0 ms"},"name":"Log","tid":250490,"ts":578294840.75300002},{"pid":0,"ph":"X","args":{},"dur":350.19900000095367,"name":"codeAction","tid":250490,"ts":578294544.71800005},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":35,"line":18},"start":{"character":27,"line":18}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":35,"line":18},"start":{"character":27,"line":18}},"tweakID":"ExtractVariable"}],"command":"clangd.applyTweak","title":"Extract subexpression to variable"},"kind":"refactor","title":"Extract subexpression to variable"}]},"dur":90.161999940872192,"name":"textDocument/codeAction","tid":241390,"ts":578294426.19200003},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":578294938.50999999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(92)"},"name":"Log","tid":241390,"ts":578393453.23800004},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":254368},{"pid":0,"ph":"X","args":{},"dur":11.101000070571899,"name":"WaitForFreeSemaphoreSlot","tid":254368,"ts":578393569.51999998},{"pid":0,"ph":"s","cat":"mock_cat","id":106,"name":"Context crosses threads","tid":241390,"ts":578393441.68599999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":106,"name":"Context crosses threads","tid":254368,"ts":578393599.32700002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":17.404000043869019,"name":"getConfig","tid":254368,"ts":578393592.33299994},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(92) 0 ms"},"name":"Log","tid":254368,"ts":578393953.80299997},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endCharacter":67,"endLine":19,"kind":"region","startCharacter":8,"startLine":18},{"endCharacter":66,"endLine":19,"kind":"region","startCharacter":26,"startLine":18},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":108.32700002193451,"name":"textDocument/foldingRange","tid":241390,"ts":578393441.68599999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(93)"},"name":"Log","tid":241390,"ts":580850257.70200002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.1260000467300415,"name":"Queued:codeAction","tid":241390,"ts":580850313.43799996},{"pid":0,"ph":"s","cat":"mock_cat","id":107,"name":"Context crosses threads","tid":241390,"ts":580850233.84599996},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":107,"name":"Context crosses threads","tid":250490,"ts":580850360.98899996},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":13.555999994277954,"name":"getConfig","tid":250490,"ts":580850367.301},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(93) 0 ms"},"name":"Log","tid":250490,"ts":580850468.704},{"pid":0,"ph":"X","args":{},"dur":148.42300009727478,"name":"codeAction","tid":250490,"ts":580850350.92999995},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":34,"line":22},"start":{"character":34,"line":22}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":89.962000012397766,"name":"textDocument/codeAction","tid":241390,"ts":580850233.84599996},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":580850537.83599997},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(94)"},"name":"Log","tid":241390,"ts":581371702.26499999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.7549999952316284,"name":"Queued:codeAction","tid":241390,"ts":581371762.04900002},{"pid":0,"ph":"s","cat":"mock_cat","id":108,"name":"Context crosses threads","tid":241390,"ts":581371670.78499997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":108,"name":"Context crosses threads","tid":250490,"ts":581371826.82299995},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":11.712999939918518,"name":"getConfig","tid":250490,"ts":581371833.47500002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(94) 0 ms"},"name":"Log","tid":250490,"ts":581372042.19400001},{"pid":0,"ph":"X","args":{},"dur":253.22299993038177,"name":"codeAction","tid":250490,"ts":581371817.17400002},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":19,"line":20},"start":{"character":19,"line":20}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":99.971000075340271,"name":"textDocument/codeAction","tid":241390,"ts":581371670.78499997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":581372110.454},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(95)"},"name":"Log","tid":241390,"ts":581675114.65600002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.3059999942779541,"name":"Queued:codeAction","tid":241390,"ts":581675173.63900006},{"pid":0,"ph":"s","cat":"mock_cat","id":109,"name":"Context crosses threads","tid":241390,"ts":581675089.40799999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":109,"name":"Context crosses threads","tid":250490,"ts":581675299.16799998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.21999990940094,"name":"getConfig","tid":250490,"ts":581675311.81200004},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(95) 0 ms"},"name":"Log","tid":250490,"ts":581675476.08599997},{"pid":0,"ph":"X","args":{},"dur":289.87399995326996,"name":"codeAction","tid":250490,"ts":581675235.727},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":16,"line":20},"start":{"character":16,"line":20}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":96.434000015258789,"name":"textDocument/codeAction","tid":241390,"ts":581675089.40799999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":581675552.40199995},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/hover(96)"},"name":"Log","tid":241390,"ts":581733823.72000003},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.7230000495910645,"name":"Queued:Hover","tid":241390,"ts":581733859.03799999},{"pid":0,"ph":"s","cat":"mock_cat","id":110,"name":"Context crosses threads","tid":241390,"ts":581733808.21099997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":110,"name":"Context crosses threads","tid":250490,"ts":581733929.59200001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":9.0169999599456787,"name":"getConfig","tid":250490,"ts":581733933.69000006},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/hover(96) 0 ms"},"name":"Log","tid":250490,"ts":581734239.824},{"pid":0,"ph":"X","args":{},"dur":357.59300005435944,"name":"Hover","tid":250490,"ts":581733919.222},{"pid":0,"ph":"X","args":{"Params":{"position":{"character":16,"line":21},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"contents":{"kind":"markdown","value":"### `string-literal`  \n\n---\nType: `const char[42]`  \nSize: 42 bytes"},"range":{"end":{"character":26,"line":21},"start":{"character":6,"line":21}}}},"dur":60.40500009059906,"name":"textDocument/hover","tid":241390,"ts":581733808.21099997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":581734303.63600004},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/hover(97)"},"name":"Log","tid":241390,"ts":582396253.49100006},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.0740000009536743,"name":"Queued:Hover","tid":241390,"ts":582396295.58200002},{"pid":0,"ph":"s","cat":"mock_cat","id":111,"name":"Context crosses threads","tid":241390,"ts":582396225.53799999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":111,"name":"Context crosses threads","tid":250490,"ts":582396349.88499999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":18.404999971389771,"name":"getConfig","tid":250490,"ts":582396355.30599999},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/hover(97) 0 ms"},"name":"Log","tid":250490,"ts":582396683.85300004},{"pid":0,"ph":"X","args":{},"dur":390.6360000371933,"name":"Hover","tid":250490,"ts":582396343.54299998},{"pid":0,"ph":"X","args":{"Params":{"position":{"character":14,"line":21},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"contents":{"kind":"markdown","value":"### `string-literal`  \n\n---\nType: `const char[42]`  \nSize: 42 bytes"},"range":{"end":{"character":26,"line":21},"start":{"character":6,"line":21}}}},"dur":80.282999992370605,"name":"textDocument/hover","tid":241390,"ts":582396225.53799999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":582396827.11600006},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(98)"},"name":"Log","tid":241390,"ts":582480760.324},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.843000054359436,"name":"Queued:codeAction","tid":241390,"ts":582480797.40499997},{"pid":0,"ph":"s","cat":"mock_cat","id":112,"name":"Context crosses threads","tid":241390,"ts":582480743.70200002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":112,"name":"Context crosses threads","tid":250490,"ts":582480832.92200005},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":9.2380000352859497,"name":"getConfig","tid":250490,"ts":582480837.01999998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(98) 0 ms"},"name":"Log","tid":250490,"ts":582480994.02999997},{"pid":0,"ph":"X","args":{},"dur":192.16699993610382,"name":"codeAction","tid":250490,"ts":582480826.62},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":15,"line":21},"start":{"character":15,"line":21}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":60.034999966621399,"name":"textDocument/codeAction","tid":241390,"ts":582480743.70200002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":582481041.46000004},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(99)"},"name":"Log","tid":241390,"ts":583752236.76800001},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.9160000085830688,"name":"Queued:codeAction","tid":241390,"ts":583752285.41100001},{"pid":0,"ph":"s","cat":"mock_cat","id":113,"name":"Context crosses threads","tid":241390,"ts":583752201.31099999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":113,"name":"Context crosses threads","tid":250490,"ts":583752334.48500001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":159.43400001525879,"name":"getConfig","tid":250490,"ts":583752338.62300003},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(99) 0 ms"},"name":"Log","tid":250490,"ts":583752655.68799996},{"pid":0,"ph":"X","args":{},"dur":370.0460000038147,"name":"codeAction","tid":250490,"ts":583752328.29299998},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":28,"line":19},"start":{"character":23,"line":19}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":28,"line":19},"start":{"character":23,"line":19}},"tweakID":"ExtractVariable"}],"command":"clangd.applyTweak","title":"Extract subexpression to variable"},"kind":"refactor","title":"Extract subexpression to variable"}]},"dur":93.398000001907349,"name":"textDocument/codeAction","tid":241390,"ts":583752201.31099999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":583752723.86800003},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(100)"},"name":"Log","tid":241390,"ts":584050601.35500002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.9340000152587891,"name":"Queued:codeAction","tid":241390,"ts":584050672.69099998},{"pid":0,"ph":"s","cat":"mock_cat","id":114,"name":"Context crosses threads","tid":241390,"ts":584050585.57500005},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":114,"name":"Context crosses threads","tid":250490,"ts":584050760.34899998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":12.133000016212463,"name":"getConfig","tid":250490,"ts":584050767.18200004},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(100) 0 ms"},"name":"Log","tid":250490,"ts":584050983.13399994},{"pid":0,"ph":"X","args":{},"dur":325.94200003147125,"name":"codeAction","tid":250490,"ts":584050703.38},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":0,"line":20},"start":{"character":0,"line":19}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":0,"line":20},"start":{"character":0,"line":19}},"tweakID":"ExtractVariable"}],"command":"clangd.applyTweak","title":"Extract subexpression to variable"},"kind":"refactor","title":"Extract subexpression to variable"}]},"dur":94.370000004768372,"name":"textDocument/codeAction","tid":241390,"ts":584050585.57500005},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":584051057.08500004},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(101)"},"name":"Log","tid":241390,"ts":584963907.93700004},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.6750000715255737,"name":"Queued:codeAction","tid":241390,"ts":584963948.79499996},{"pid":0,"ph":"s","cat":"mock_cat","id":115,"name":"Context crosses threads","tid":241390,"ts":584963884.06099999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":115,"name":"Context crosses threads","tid":250490,"ts":584963987.028},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":9.4790000915527344,"name":"getConfig","tid":250490,"ts":584963991.60599995},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(101) 0 ms"},"name":"Log","tid":250490,"ts":584964147.81500006},{"pid":0,"ph":"X","args":{},"dur":192.84799993038177,"name":"codeAction","tid":250490,"ts":584963980.65600002},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":9,"line":18},"start":{"character":9,"line":18}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":75.473999977111816,"name":"textDocument/codeAction","tid":241390,"ts":584963884.06099999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":584964197.87},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/hover(102)"},"name":"Log","tid":241390,"ts":585118284.88499999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.8259999752044678,"name":"Queued:Hover","tid":241390,"ts":585118346.97300005},{"pid":0,"ph":"s","cat":"mock_cat","id":116,"name":"Context crosses threads","tid":241390,"ts":585118260.08800006},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":116,"name":"Context crosses threads","tid":250490,"ts":585118410.21399999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":9.5080000162124634,"name":"getConfig","tid":250490,"ts":585118414.773},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":585118802.78299999},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":585118845.64499998},{"pid":0,"ph":"X","args":{},"dur":133.875,"name":"Hover::maybeAddSymbolProviders","tid":250490,"ts":585118769.921},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/hover(102) 1 ms"},"name":"Log","tid":250490,"ts":585119775.60000002},{"pid":0,"ph":"X","args":{},"dur":1423.347000002861,"name":"Hover","tid":250490,"ts":585118403.76199996},{"pid":0,"ph":"X","args":{"Params":{"position":{"character":4,"line":18},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"contents":{"kind":"markdown","value":"### macro `IFDEF`  \nprovided by `\"macro.h\"`  \n\n---\n```cpp\n#define IFDEF(macro, ...) MUXDEF(macro, __KEEP, __IGNORE)(__VA_ARGS__)\n\n// Expands to\ndo {\n  printf(\"\\33[1;34m\"\n         \"[%s:%d %s] \"\n         \"If trace is enabled, a log file will be generated \"\n         \"to record the trace. This may lead to a large log file. \"\n         \"If it is not necessary, you can disable it in menuconfig\"\n         \"\\33[0m\"\n         \"\\n\",\n         \"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/\"\n         \"ysyx-workbench/npc/csrc/monitor/monitor.cpp\",\n         21, __func__);\n  do {\n    extern FILE *log_fp;\n    extern bool log_enable();\n    if (log_enable()) {\n      fprintf(log_fp,\n              \"\\33[1;34m\"\n              \"[%s:%d %s] \"\n              \"If trace is enabled, a log file will be generated \"\n              \"to record the trace. This may lead to a large log file. \"\n              \"If it is not necessary, you can disable it in menuconfig\"\n              \"\\33[0m\"\n              \"\\n\",\n              \"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/\"\n              \"ysyx-workbench/npc/csrc/monitor/monitor.cpp\",\n              21, __func__);\n      fflush(log_fp);\n    }\n  } while (0);\n} while (0)\n```"},"range":{"end":{"character":7,"line":18},"start":{"character":2,"line":18}}}},"dur":94.830999970436096,"name":"textDocument/hover","tid":241390,"ts":585118260.08800006},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":585119861.53400004},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(103)"},"name":"Log","tid":241390,"ts":585379653.34000003},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.0840001106262207,"name":"Queued:codeAction","tid":241390,"ts":585379700.30999994},{"pid":0,"ph":"s","cat":"mock_cat","id":117,"name":"Context crosses threads","tid":241390,"ts":585379634.14300001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":117,"name":"Context crosses threads","tid":250490,"ts":585379745.296},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":9.8990000486373901,"name":"getConfig","tid":250490,"ts":585379749.94400001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(103) 0 ms"},"name":"Log","tid":250490,"ts":585379995.29299998},{"pid":0,"ph":"X","args":{},"dur":312.74699997901917,"name":"codeAction","tid":250490,"ts":585379738.68299997},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":5,"line":18},"start":{"character":5,"line":18}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":5,"line":18},"start":{"character":5,"line":18}},"tweakID":"ExpandMacro"}],"command":"clangd.applyTweak","title":"Expand macro 'IFDEF'"},"kind":"refactor","title":"Expand macro 'IFDEF'"},{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":5,"line":18},"start":{"character":5,"line":18}},"tweakID":"ExtractFunction"}],"command":"clangd.applyTweak","title":"Extract to function"},"kind":"refactor","title":"Extract to function"}]},"dur":73.330000042915344,"name":"textDocument/codeAction","tid":241390,"ts":585379634.14300001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":585380116.77499998},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/hover(104)"},"name":"Log","tid":241390,"ts":585951623.778},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.4950000047683716,"name":"Queued:Hover","tid":241390,"ts":585951700.77499998},{"pid":0,"ph":"s","cat":"mock_cat","id":118,"name":"Context crosses threads","tid":241390,"ts":585951593.24000001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":118,"name":"Context crosses threads","tid":250490,"ts":585951760.36899996},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":27.33299994468689,"name":"getConfig","tid":250490,"ts":585951768.86500001},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":585952309.79799998},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":585952338.05200005},{"pid":0,"ph":"X","args":{},"dur":104.5900000333786,"name":"Hover::maybeAddSymbolProviders","tid":250490,"ts":585952269.16999996},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/hover(104) 1 ms"},"name":"Log","tid":250490,"ts":585953596.75399995},{"pid":0,"ph":"X","args":{},"dur":1911.6089999675751,"name":"Hover","tid":250490,"ts":585951748.99699998},{"pid":0,"ph":"X","args":{"Params":{"position":{"character":2,"line":18},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"contents":{"kind":"markdown","value":"### macro `IFDEF`  \nprovided by `\"macro.h\"`  \n\n---\n```cpp\n#define IFDEF(macro, ...) MUXDEF(macro, __KEEP, __IGNORE)(__VA_ARGS__)\n\n// Expands to\ndo {\n  printf(\"\\33[1;34m\"\n         \"[%s:%d %s] \"\n         \"If trace is enabled, a log file will be generated \"\n         \"to record the trace. This may lead to a large log file. \"\n         \"If it is not necessary, you can disable it in menuconfig\"\n         \"\\33[0m\"\n         \"\\n\",\n         \"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/\"\n         \"ysyx-workbench/npc/csrc/monitor/monitor.cpp\",\n         21, __func__);\n  do {\n    extern FILE *log_fp;\n    extern bool log_enable();\n    if (log_enable()) {\n      fprintf(log_fp,\n              \"\\33[1;34m\"\n              \"[%s:%d %s] \"\n              \"If trace is enabled, a log file will be generated \"\n              \"to record the trace. This may lead to a large log file. \"\n              \"If it is not necessary, you can disable it in menuconfig\"\n              \"\\33[0m\"\n              \"\\n\",\n              \"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/\"\n              \"ysyx-workbench/npc/csrc/monitor/monitor.cpp\",\n              21, __func__);\n      fflush(log_fp);\n    }\n  } while (0);\n} while (0)\n```"},"range":{"end":{"character":7,"line":18},"start":{"character":2,"line":18}}}},"dur":117.63499999046326,"name":"textDocument/hover","tid":241390,"ts":585951593.24000001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":585953707.80599999},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(105)"},"name":"Log","tid":241390,"ts":587234502.68599999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.8539999723434448,"name":"Queued:codeAction","tid":241390,"ts":587234552.12},{"pid":0,"ph":"s","cat":"mock_cat","id":119,"name":"Context crosses threads","tid":241390,"ts":587234477.75800002},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":119,"name":"Context crosses threads","tid":250490,"ts":587234665.55700004},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":11.070999979972839,"name":"getConfig","tid":250490,"ts":587234674.61399996},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(105) 0 ms"},"name":"Log","tid":250490,"ts":587234946.24300003},{"pid":0,"ph":"X","args":{},"dur":397.7389999628067,"name":"codeAction","tid":250490,"ts":587234606.82500005},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":69,"line":20},"start":{"character":2,"line":18}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":69,"line":20},"start":{"character":2,"line":18}},"tweakID":"ExpandMacro"}],"command":"clangd.applyTweak","title":"Expand macro 'IFDEF'"},"kind":"refactor","title":"Expand macro 'IFDEF'"},{"command":{"arguments":[{"file":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","selection":{"end":{"character":69,"line":20},"start":{"character":2,"line":18}},"tweakID":"ExtractFunction"}],"command":"clangd.applyTweak","title":"Extract to function"},"kind":"refactor","title":"Extract to function"}]},"dur":81.365999937057495,"name":"textDocument/codeAction","tid":241390,"ts":587234477.75800002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":587235044.21000004},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/hover(106)"},"name":"Log","tid":241390,"ts":588568716.25300002},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.6950000524520874,"name":"Queued:Hover","tid":241390,"ts":588568771.71899998},{"pid":0,"ph":"s","cat":"mock_cat","id":120,"name":"Context crosses threads","tid":241390,"ts":588568699.66100001},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":120,"name":"Context crosses threads","tid":250490,"ts":588568816.01300001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":19.607000112533569,"name":"getConfig","tid":250490,"ts":588568821.63399994},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":588568999.69400001},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":588569018.95099998},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/hover(106) 0 ms"},"name":"Log","tid":250490,"ts":588569327.23899996},{"pid":0,"ph":"X","args":{},"dur":551.60300004482269,"name":"Hover","tid":250490,"ts":588568809.69099998},{"pid":0,"ph":"X","args":{"Params":{"position":{"character":10,"line":0},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"contents":{"kind":"markdown","value":"### `common.h`  \n\n---\n```\n/home/<USER>/Desktop/ysyx-workbench/nemu/include/common.h\n```"}}},"dur":85.292999982833862,"name":"textDocument/hover","tid":241390,"ts":588568699.66100001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":588569393.22500002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(107)"},"name":"Log","tid":241390,"ts":588720668.21200001},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.0329999923706055,"name":"Queued:codeAction","tid":241390,"ts":588720713.97000003},{"pid":0,"ph":"s","cat":"mock_cat","id":121,"name":"Context crosses threads","tid":241390,"ts":588720614.21899998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":121,"name":"Context crosses threads","tid":250490,"ts":588720797.89999998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":12.664000034332275,"name":"getConfig","tid":250490,"ts":588720804.46200001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(107) 0 ms"},"name":"Log","tid":250490,"ts":588720859.81799996},{"pid":0,"ph":"X","args":{},"dur":135.18800008296967,"name":"codeAction","tid":250490,"ts":588720755.73899996},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[{"code":"1696","message":"#include errors detected. Please update your includePath. Squiggles are disabled for this translation unit (/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp).","range":{"end":{"character":19,"line":0},"start":{"character":0,"line":0}},"severity":1,"source":"C/C++"},{"code":"1696","message":"cannot open source file \"common.h\"","range":{"end":{"character":19,"line":0},"start":{"character":0,"line":0}},"severity":1,"source":"C/C++"}],"only":["quickfix"],"triggerKind":1},"range":{"end":{"character":19,"line":0},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":109.16799998283386,"name":"textDocument/codeAction","tid":241390,"ts":588720614.21899998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":588720930.28199995},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/didChange"},"name":"Log","tid":241390,"ts":591073373.89999998},{"pid":0,"ph":"s","cat":"mock_cat","id":122,"name":"Context crosses threads","tid":241390,"ts":591073350.94700003},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":122,"name":"Context crosses threads","tid":250490,"ts":591073488.51900005},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":591073507.546},{"pid":0,"ph":"X","args":{},"dur":216.74400007724762,"name":"ProfileBrief","tid":241390,"ts":591073474.69299996},{"pid":0,"ph":"X","args":{"next_request":"Update","sleep_ms":49},"dur":50053.464000105858,"name":"Debounce","tid":250490,"ts":591073476.48599994},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.5669999122619629,"name":"Queued:Update","tid":241390,"ts":591073457.33000004},{"pid":0,"ph":"s","cat":"mock_cat","id":123,"name":"Context crosses threads","tid":241390,"ts":591073350.94700003},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":123,"name":"Context crosses threads","tid":250490,"ts":591123571.49899995},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":178.98099994659424,"name":"getConfig","tid":250490,"ts":591123576.12800002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":7.5549999475479126,"name":"getConfig","tid":250490,"ts":591123770.26800001},{"pid":0,"ph":"i","args":{"Message":"Failed to find compilation database for /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"name":"Log","tid":250490,"ts":591123974.829},{"pid":0,"ph":"X","args":{},"dur":53.421999931335449,"name":"AdjustCompileFlags","tid":250490,"ts":591124005.44700003},{"pid":0,"ph":"i","args":{"Message":"ASTWorker building file /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp version 3 with command clangd fallback\n[/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor]\n/usr/lib/llvm-14/bin/clang -I/home/<USER>/Desktop/ysyx-workbench/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/nemu/src/isa/riscv32/include/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/build/obj_dir/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/klib/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/src/platform/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include -I/home/<USER>/Desktop/ysyx-workbench/nvboard/include/ -I/usr/include/SDL2/ -I/usr/share/verilator/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/csrc/include -include=/headers/file.h -I/other/headers -resource-dir=/home/<USER>/.config/Code/User/globalStorage/llvm-vs-code-extensions.vscode-clangd/install/19.1.2/clangd_19.1.2/lib/clang/19 -- /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"name":"Log","tid":250490,"ts":591124365.625},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250491,"ts":591125763.99399996},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":1662.482999920845,"name":"CreatePreamblePatch","tid":250490,"ts":591125791.52600002},{"pid":0,"ph":"X","args":{},"dur":194.16100001335144,"name":"ClangTidyOpts","tid":250490,"ts":591127520.977},{"pid":0,"ph":"X","args":{},"dur":1330.0889999866486,"name":"ClangTidyInit","tid":250490,"ts":591128344.39900005},{"pid":0,"ph":"X","args":{"results":0},"dur":6.0109999179840088,"name":"MemIndex fuzzyFind","tid":250490,"ts":591134071.46000004},{"pid":0,"ph":"X","args":{"query":"(LIMIT 10000 (& T=cpu S= ?=Restricted For Code Completion))"},"dur":23.986000061035156,"name":"Dex fuzzyFind","tid":250490,"ts":591134091.32799995},{"pid":0,"ph":"X","args":{"dynamic":0,"merged":0,"static":2,"static_dropped":0},"dur":54.424000024795532,"name":"MergedIndex fuzzyFind","tid":250490,"ts":591134068.42400002},{"pid":0,"ph":"X","args":{"results":0},"dur":1.6129999160766602,"name":"MemIndex fuzzyFind","tid":250490,"ts":591134131.14400005},{"pid":0,"ph":"X","args":{},"dur":1.5830000638961792,"name":"ProjectAwareIndex::fuzzyFind","tid":250490,"ts":591134137.33599997},{"pid":0,"ph":"X","args":{"dynamic":0,"merged":0,"static":0,"static_dropped":0},"dur":13.125,"name":"MergedIndex fuzzyFind","tid":250490,"ts":591134129.87100005},{"pid":0,"ph":"X","args":{"dynamic":2,"merged":0,"static":0,"static_dropped":0},"dur":85.583999991416931,"name":"MergedIndex fuzzyFind","tid":250490,"ts":591134063.33399999},{"pid":0,"ph":"X","args":{},"dur":1052.789999961853,"name":"ClangTidyMatch","tid":250490,"ts":591135342.66600001},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":591136455.079},{"pid":0,"ph":"i","args":{"Message":"IncludeCleaner: Failed to get an entry for resolved path : No such file or directory"},"name":"Log","tid":250490,"ts":591136493.11199999},{"pid":0,"ph":"X","args":{},"dur":4.187999963760376,"name":"IncludeCleaner::getUnused","tid":250490,"ts":591136939.65400004},{"pid":0,"ph":"X","args":{},"dur":287.84900009632111,"name":"include_cleaner::walkUsed","tid":250490,"ts":591136662.41499996},{"pid":0,"ph":"X","args":{},"dur":376.70899999141693,"name":"IncludeCleaner::issueIncludeCleanerDiagnostics","tid":250490,"ts":591136956.91700006},{"pid":0,"ph":"X","args":{"File":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":11694.419999957085,"name":"BuildAST","tid":250490,"ts":591125736.70200002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/publishDiagnostics"},"name":"Log","tid":250490,"ts":591137945.52400005},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/inactiveRegions"},"name":"Log","tid":250490,"ts":591137987.48399997},{"pid":0,"ph":"X","args":{},"dur":176.76800000667572,"name":"ASTSignals::derive","tid":250490,"ts":591138002.80299997},{"pid":0,"ph":"X","args":{},"dur":718.95200002193451,"name":"Running main AST callback","tid":250490,"ts":591137466.17900002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":8.8470000028610229,"name":"getConfig","tid":250490,"ts":591138199.54900002},{"pid":0,"ph":"X","args":{},"dur":76.205000042915344,"name":"Build AST","tid":250490,"ts":591138197.78600001},{"pid":0,"ph":"X","args":{},"dur":14629.182000041008,"name":"Update","tid":250490,"ts":591123564.60599995},{"pid":0,"ph":"X","args":{"Params":{"contentChanges":[{"range":{"end":{"character":2,"line":20},"start":{"character":2,"line":20}},"rangeLength":0,"text":"// "},{"range":{"end":{"character":2,"line":19},"start":{"character":2,"line":19}},"rangeLength":0,"text":"// "},{"range":{"end":{"character":2,"line":18},"start":{"character":2,"line":18}},"rangeLength":0,"text":"// "}],"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp","version":3}}},"dur":351.13999998569489,"name":"textDocument/didChange","tid":241390,"ts":591073350.94700003},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":591138324.37699997},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(108)"},"name":"Log","tid":241390,"ts":591265122.55700004},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.9140000343322754,"name":"Queued:InlayHints","tid":241390,"ts":591265164.92799997},{"pid":0,"ph":"s","cat":"mock_cat","id":124,"name":"Context crosses threads","tid":241390,"ts":591265107.59899998},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":124,"name":"Context crosses threads","tid":250490,"ts":591265233.77999997},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":9.9089999198913574,"name":"getConfig","tid":250490,"ts":591265238.11800003},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(108) 0 ms"},"name":"Log","tid":250490,"ts":591265958.84300005},{"pid":0,"ph":"X","args":{},"dur":1164.472000002861,"name":"InlayHints","tid":250490,"ts":591265227.01699996},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":3,"line":114},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":9,"line":22}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":41}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":41}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":41}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":41}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":41}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":42}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":42}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":42}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":42}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":42}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":43}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":43}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":43}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":43}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":43}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":44}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":44}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":44}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":44}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":44}},{"label":[{"value":"[4]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":45}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":45}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":45}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":45}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":45}},{"label":[{"value":"[5]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":46}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":46}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":46}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":46}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":48,"line":46}},{"kind":2,"label":[{"value":"shortopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":41,"line":49}},{"kind":2,"label":[{"value":"longopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":56,"line":49}},{"kind":2,"label":[{"value":"longind:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":63,"line":49}},{"kind":2,"label":[{"value":"s:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":25,"line":53}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":33,"line":53}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":35,"line":55}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":42,"line":55}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":58}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":59}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":60}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":61}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":62}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":63}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":64}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":65}},{"kind":2,"label":[{"value":"status:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":66}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":81}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":32,"line":81}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":84}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":84}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":84}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":23,"line":85}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":89}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":89}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":89}},{"kind":2,"label":[{"value":"ptr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":90}},{"kind":2,"label":[{"value":"paddr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":36,"line":90}},{"kind":2,"label":[{"value":"n:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":57,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":60,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":12,"line":93}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":98}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":99}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":100}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":101}}]},"dur":65.144000053405762,"name":"textDocument/inlayHint","tid":241390,"ts":591265107.59899998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":591266595.01699996},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(109)"},"name":"Log","tid":241390,"ts":591279528.80400002},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":254533},{"pid":0,"ph":"X","args":{},"dur":19.718000054359436,"name":"WaitForFreeSemaphoreSlot","tid":254533,"ts":591279641.278},{"pid":0,"ph":"s","cat":"mock_cat","id":125,"name":"Context crosses threads","tid":241390,"ts":591279512.472},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":125,"name":"Context crosses threads","tid":254533,"ts":591279676.85599995},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":17.243000030517578,"name":"getConfig","tid":254533,"ts":591279670.97500002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(109) 0 ms"},"name":"Log","tid":254533,"ts":591280018.79900002},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":72,"endLine":20,"kind":"comment","startCharacter":4,"startLine":17},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":95.101999998092651,"name":"textDocument/foldingRange","tid":241390,"ts":591279512.472},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(110)"},"name":"Log","tid":241390,"ts":591390261.57099998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.1139999628067017,"name":"Queued:SemanticHighlights","tid":241390,"ts":591390297.93099999},{"pid":0,"ph":"s","cat":"mock_cat","id":126,"name":"Context crosses threads","tid":241390,"ts":591390249.70899999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":126,"name":"Context crosses threads","tid":250490,"ts":591390333.75899994},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":19.067000031471252,"name":"getConfig","tid":250490,"ts":591390343.67799997},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(110) 0 ms"},"name":"Log","tid":250490,"ts":591390668.22800004},{"pid":0,"ph":"X","args":{},"dur":412.68700003623962,"name":"SemanticHighlights","tid":250490,"ts":591390326.65600002},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"data":[8,5,8,3,131073,1,5,9,3,131073,1,5,8,3,131073,0,21,8,2,16403,1,5,8,3,131073,1,5,13,3,131073,0,20,11,2,16387,0,18,8,2,16387,0,14,4,2,16387,4,5,7,3,131075,5,2,3,19,131072,1,2,6,3,131584,2,2,6,19,131072,5,5,18,3,131073,2,13,8,0,65539,0,11,4,19,131072,1,13,12,0,65539,0,15,4,19,131072,1,13,8,0,65539,0,11,4,19,131072,1,0,4,8,66048,0,6,8,0,131075,0,11,4,19,131072,1,11,13,0,65539,3,12,11,3,131073,1,11,10,3,65539,0,15,4,2,16387,0,12,4,2,16387,1,17,6,8,131584,0,7,5,1,16403,1,22,11,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,41,4,19,131072,2,8,1,1,16387,1,13,1,1,16384,0,2,1,21,0,0,2,11,3,131584,0,12,4,2,16384,0,6,4,2,16384,0,21,5,1,16400,0,7,4,19,131072,0,7,2,21,0,0,3,1,21,0,1,14,1,1,16384,1,18,18,3,131072,1,18,8,0,65536,0,9,1,21,0,0,2,6,0,131584,1,18,6,3,131584,0,7,6,0,131584,0,14,1,21,0,0,1,13,0,65536,1,18,12,0,65536,0,13,1,21,0,0,2,6,0,131584,1,18,8,0,131072,0,9,1,21,0,0,2,5,3,131584,0,6,6,0,131584,0,13,11,3,131072,1,16,8,0,65536,0,9,1,21,0,0,2,6,0,131584,2,10,6,3,131584,0,49,4,2,16384,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,4,3,131584,10,12,8,3,65539,1,8,8,0,65536,0,9,2,21,0,0,3,4,19,131072,1,6,3,19,131072,3,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,7,8,0,65536,1,4,6,19,131072,0,8,2,1,16384,0,25,8,0,65536,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,9,4,1,16387,0,7,5,3,131584,0,7,2,1,18432,2,4,3,19,131072,0,36,8,0,65536,0,10,4,1,16384,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,8,3,1,16387,0,6,5,3,131584,0,7,13,3,131072,0,15,12,19,131072,0,15,4,1,16384,0,9,2,1,18432,1,4,6,19,131072,0,8,3,1,16384,0,4,2,21,0,2,4,6,3,131584,0,8,2,1,18432,1,11,4,1,16384,3,13,8,18,66048,0,9,3,0,65555,7,12,7,3,65539,2,11,12,19,131072,9,5,8,3,131075,2,2,6,3,131584,0,7,13,3,131072,0,14,12,19,131072,0,15,3,0,65552,0,12,3,0,65552,3,2,7,3,65536,3,5,12,3,131075,0,18,4,2,16387,0,12,4,2,16387,3,4,10,3,65536,0,12,4,2,16384,0,6,4,2,18432,2,4,9,3,131072,3,4,8,3,131072,0,10,8,0,65536,3,4,8,3,131072,2,4,8,3,131072,2,9,8,1,16387,0,11,8,3,65536,7,4,7,3,131072],"resultId":"6"}},"dur":54.023000001907349,"name":"textDocument/semanticTokens/full","tid":241390,"ts":591390249.70899999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":591390782.03499997},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(111)"},"name":"Log","tid":241390,"ts":591391456.472},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.9440000057220459,"name":"Queued:codeAction","tid":241390,"ts":591391482.36099994},{"pid":0,"ph":"s","cat":"mock_cat","id":127,"name":"Context crosses threads","tid":241390,"ts":591391442.926},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":127,"name":"Context crosses threads","tid":250490,"ts":591391541.24399996},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":8.1059999465942383,"name":"getConfig","tid":250490,"ts":591391546.81400001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(111) 0 ms"},"name":"Log","tid":250490,"ts":591391593.79400003},{"pid":0,"ph":"X","args":{},"dur":114.39800000190735,"name":"codeAction","tid":250490,"ts":591391502.53999996},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":72,"line":20},"start":{"character":5,"line":18}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":45.626999974250793,"name":"textDocument/codeAction","tid":241390,"ts":591391442.926},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":591391642.69700003},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentSymbol(112)"},"name":"Log","tid":241390,"ts":591429897.07099998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.9159998893737793,"name":"Queued:DocumentSymbols","tid":241390,"ts":591429941.99600005},{"pid":0,"ph":"s","cat":"mock_cat","id":128,"name":"Context crosses threads","tid":241390,"ts":591429880.88},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":128,"name":"Context crosses threads","tid":250490,"ts":591429981.68200004},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(113)"},"name":"Log","tid":241390,"ts":591429983.13499999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":14.087000012397766,"name":"getConfig","tid":250490,"ts":591429989.60699999},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":254534},{"pid":0,"ph":"X","args":{},"dur":11.301999926567078,"name":"WaitForFreeSemaphoreSlot","tid":254534,"ts":591430075.10000002},{"pid":0,"ph":"s","cat":"mock_cat","id":129,"name":"Context crosses threads","tid":241390,"ts":591429963.50699997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":129,"name":"Context crosses threads","tid":254534,"ts":591430099.68700004},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":11.742000102996826,"name":"getConfig","tid":254534,"ts":591430094.82799995},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentSymbol(112) 0 ms"},"name":"Log","tid":250490,"ts":591430405.301},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(113) 0 ms"},"name":"Log","tid":254534,"ts":591430445.50699997},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":72,"endLine":20,"kind":"comment","startCharacter":4,"startLine":17},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":92.175999999046326,"name":"textDocument/foldingRange","tid":241390,"ts":591429963.50699997},{"pid":0,"ph":"X","args":{},"dur":695.48699998855591,"name":"DocumentSymbols","tid":250490,"ts":591429974.21800005},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"detail":"void ()","kind":12,"name":"init_sdb","range":{"end":{"character":15,"line":8},"start":{"character":0,"line":8}},"selectionRange":{"end":{"character":13,"line":8},"start":{"character":5,"line":8}}},{"detail":"void ()","kind":12,"name":"init_rand","range":{"end":{"character":16,"line":9},"start":{"character":0,"line":9}},"selectionRange":{"end":{"character":14,"line":9},"start":{"character":5,"line":9}}},{"detail":"void (const char *)","kind":12,"name":"init_log","range":{"end":{"character":35,"line":10},"start":{"character":0,"line":10}},"selectionRange":{"end":{"character":13,"line":10},"start":{"character":5,"line":10}}},{"detail":"void ()","kind":12,"name":"init_mem","range":{"end":{"character":15,"line":11},"start":{"character":0,"line":11}},"selectionRange":{"end":{"character":13,"line":11},"start":{"character":5,"line":11}}},{"detail":"void (char *, long, int)","kind":12,"name":"init_difftest","range":{"end":{"character":62,"line":12},"start":{"character":0,"line":12}},"selectionRange":{"end":{"character":18,"line":12},"start":{"character":5,"line":12}}},{"detail":"void ()","kind":12,"name":"welcome","range":{"end":{"character":1,"line":25},"start":{"character":0,"line":16}},"selectionRange":{"end":{"character":12,"line":16},"start":{"character":5,"line":16}}},{"detail":"void ()","kind":12,"name":"sdb_set_batch_mode","range":{"end":{"character":25,"line":29},"start":{"character":0,"line":29}},"selectionRange":{"end":{"character":23,"line":29},"start":{"character":5,"line":29}}},{"detail":"char *","kind":13,"name":"log_file","range":{"end":{"character":28,"line":31},"start":{"character":0,"line":31}},"selectionRange":{"end":{"character":21,"line":31},"start":{"character":13,"line":31}}},{"detail":"char *","kind":13,"name":"diff_so_file","range":{"end":{"character":32,"line":32},"start":{"character":0,"line":32}},"selectionRange":{"end":{"character":25,"line":32},"start":{"character":13,"line":32}}},{"detail":"char *","kind":13,"name":"img_file","range":{"end":{"character":28,"line":33},"start":{"character":0,"line":33}},"selectionRange":{"end":{"character":21,"line":33},"start":{"character":13,"line":33}}},{"detail":"FILE *","kind":13,"name":"elf_file","range":{"end":{"character":21,"line":34},"start":{"character":0,"line":34}},"selectionRange":{"end":{"character":14,"line":34},"start":{"character":6,"line":34}}},{"detail":"int","kind":13,"name":"difftest_port","range":{"end":{"character":31,"line":35},"start":{"character":0,"line":35}},"selectionRange":{"end":{"character":24,"line":35},"start":{"character":11,"line":35}}},{"detail":"void ()","kind":12,"name":"ftrace_init","range":{"end":{"character":25,"line":38},"start":{"character":0,"line":38}},"selectionRange":{"end":{"character":23,"line":38},"start":{"character":12,"line":38}}},{"detail":"int (int, char **)","kind":12,"name":"parse_args","range":{"end":{"character":1,"line":71},"start":{"character":0,"line":39}},"selectionRange":{"end":{"character":21,"line":39},"start":{"character":11,"line":39}}},{"detail":"long ()","kind":12,"name":"load_img","range":{"end":{"character":1,"line":95},"start":{"character":0,"line":76}},"selectionRange":{"end":{"character":20,"line":76},"start":{"character":12,"line":76}}},{"detail":"const uint32_t[4]","kind":13,"name":"img","range":{"end":{"character":1,"line":102},"start":{"character":0,"line":97}},"selectionRange":{"end":{"character":25,"line":97},"start":{"character":22,"line":97}}},{"detail":"void ()","kind":12,"name":"restart","range":{"end":{"character":1,"line":110},"start":{"character":0,"line":104}},"selectionRange":{"end":{"character":19,"line":104},"start":{"character":12,"line":104}}},{"detail":"void ()","kind":12,"name":"init_isa","range":{"end":{"character":1,"line":121},"start":{"character":0,"line":115}},"selectionRange":{"end":{"character":13,"line":115},"start":{"character":5,"line":115}}},{"detail":"void (int, char **)","kind":12,"name":"init_monitor","range":{"end":{"character":1,"line":146},"start":{"character":0,"line":123}},"selectionRange":{"end":{"character":17,"line":123},"start":{"character":5,"line":123}}}]},"dur":68.179999947547913,"name":"textDocument/documentSymbol","tid":241390,"ts":591429880.88},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":591430797.07799995},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(114)"},"name":"Log","tid":241390,"ts":591879974.13},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.8739999532699585,"name":"Queued:InlayHints","tid":241390,"ts":591880008.31500006},{"pid":0,"ph":"s","cat":"mock_cat","id":130,"name":"Context crosses threads","tid":241390,"ts":591879958.40999997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":130,"name":"Context crosses threads","tid":250490,"ts":591880052.88},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":11.101000070571899,"name":"getConfig","tid":250490,"ts":591880057.78999996},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(114) 0 ms"},"name":"Log","tid":250490,"ts":591880768.08500004},{"pid":0,"ph":"X","args":{},"dur":1146.066999912262,"name":"InlayHints","tid":250490,"ts":591880046.23800004},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":3,"line":114},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":9,"line":22}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":41}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":41}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":41}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":41}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":41}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":42}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":42}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":42}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":42}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":42}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":43}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":43}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":43}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":43}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":43}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":44}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":44}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":44}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":44}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":44}},{"label":[{"value":"[4]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":45}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":45}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":45}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":45}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":45}},{"label":[{"value":"[5]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":46}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":46}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":46}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":46}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":48,"line":46}},{"kind":2,"label":[{"value":"shortopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":41,"line":49}},{"kind":2,"label":[{"value":"longopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":56,"line":49}},{"kind":2,"label":[{"value":"longind:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":63,"line":49}},{"kind":2,"label":[{"value":"s:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":25,"line":53}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":33,"line":53}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":35,"line":55}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":42,"line":55}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":58}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":59}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":60}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":61}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":62}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":63}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":64}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":65}},{"kind":2,"label":[{"value":"status:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":66}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":81}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":32,"line":81}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":84}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":84}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":84}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":23,"line":85}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":89}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":89}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":89}},{"kind":2,"label":[{"value":"ptr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":90}},{"kind":2,"label":[{"value":"paddr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":36,"line":90}},{"kind":2,"label":[{"value":"n:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":57,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":60,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":12,"line":93}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":98}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":99}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":100}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":101}}]},"dur":56.007000088691711,"name":"textDocument/inlayHint","tid":241390,"ts":591879958.40999997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":591881373.90199995},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/documentLink(115)"},"name":"Log","tid":241390,"ts":592091050.34500003},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":1.9240000247955322,"name":"Queued:DocumentLinks","tid":241390,"ts":592091088.92900002},{"pid":0,"ph":"s","cat":"mock_cat","id":131,"name":"Context crosses threads","tid":241390,"ts":592091037.08000004},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":131,"name":"Context crosses threads","tid":250490,"ts":592091129.18599999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.560000061988831,"name":"getConfig","tid":250490,"ts":592091134.005},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/documentLink(115) 0 ms"},"name":"Log","tid":250490,"ts":592091224.40799999},{"pid":0,"ph":"X","args":{},"dur":172.4889999628067,"name":"DocumentLinks","tid":250490,"ts":592091121.972},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"range":{"end":{"character":19,"line":0},"start":{"character":9,"line":0}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/common.h"},{"range":{"end":{"character":25,"line":3},"start":{"character":9,"line":3}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/memory/paddr.h"},{"range":{"end":{"character":19,"line":4},"start":{"character":9,"line":4}},"target":"file:///usr/include/getopt.h"},{"range":{"end":{"character":20,"line":5},"start":{"character":9,"line":5}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/cpu/cpu.h"},{"range":{"end":{"character":25,"line":6},"start":{"character":9,"line":6}},"target":"file:///home/<USER>/Desktop/ysyx-workbench/nemu/include/cpu/difftest.h"},{"range":{"end":{"character":19,"line":27},"start":{"character":9,"line":27}},"target":"file:///usr/include/getopt.h"}]},"dur":59.232999920845032,"name":"textDocument/documentLink","tid":241390,"ts":592091037.08000004},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":592091329.25800002},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/didSave"},"name":"Log","tid":241390,"ts":592110972.78999996},{"pid":0,"ph":"i","args":{"Message":"File version went from 3 to 3"},"name":"Log","tid":241390,"ts":592111012.57599998},{"pid":0,"ph":"i","args":{"Message":"File version went from 3 to 3"},"name":"Log","tid":241390,"ts":592111103.12899995},{"pid":0,"ph":"s","cat":"mock_cat","id":132,"name":"Context crosses threads","tid":241390,"ts":592110958.48300004},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":132,"name":"Context crosses threads","tid":250490,"ts":592111128.00699997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":592111149.24699998},{"pid":0,"ph":"s","cat":"mock_cat","id":133,"name":"Context crosses threads","tid":241390,"ts":592110958.48300004},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":133,"name":"Context crosses threads","tid":250166,"ts":592111170.55799997},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":592111193.61199999},{"pid":0,"ph":"X","args":{"next_request":"Update","sleep_ms":49},"dur":50047.792999982834,"name":"Debounce","tid":250490,"ts":592111110.54299998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.2560000419616699,"name":"Queued:Update","tid":241390,"ts":592111086.25699997},{"pid":0,"ph":"s","cat":"mock_cat","id":134,"name":"Context crosses threads","tid":241390,"ts":592110958.48300004},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":134,"name":"Context crosses threads","tid":250490,"ts":592161203.23199999},{"pid":0,"ph":"X","args":{"next_request":"Update","sleep_ms":49},"dur":50048.083000063896,"name":"Debounce","tid":250166,"ts":592111156.99199998},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":18.825999975204468,"name":"Queued:Update","tid":241390,"ts":592111121.05299997},{"pid":0,"ph":"s","cat":"mock_cat","id":135,"name":"Context crosses threads","tid":241390,"ts":592110958.48300004},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":135,"name":"Context crosses threads","tid":250166,"ts":592161238.15799999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":36.730000019073486,"name":"getConfig","tid":250490,"ts":592161210.56599998},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":6.6330000162124634,"name":"getConfig","tid":250490,"ts":592161259.42900002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":38.01199996471405,"name":"getConfig","tid":250166,"ts":592161241.93599999},{"pid":0,"ph":"i","args":{"Message":"Failed to find compilation database for /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"name":"Log","tid":250490,"ts":592161293.42400002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":5.4299999475479126,"name":"getConfig","tid":250166,"ts":592161291.68099999},{"pid":0,"ph":"i","args":{"Message":"Failed to find compilation database for /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"name":"Log","tid":250166,"ts":592161329.60300004},{"pid":0,"ph":"X","args":{},"dur":64.103000044822693,"name":"AdjustCompileFlags","tid":250490,"ts":592161324.12199998},{"pid":0,"ph":"i","args":{"Message":"ASTWorker building file /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp version 3 with command clangd fallback\n[/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor]\n/usr/lib/llvm-14/bin/clang -I/home/<USER>/Desktop/ysyx-workbench/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/nemu/src/isa/riscv32/include/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/build/obj_dir/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/klib/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/src/platform/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include -I/home/<USER>/Desktop/ysyx-workbench/nvboard/include/ -I/usr/include/SDL2/ -I/usr/share/verilator/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/csrc/include -include=/headers/file.h -I/other/headers -resource-dir=/home/<USER>/.config/Code/User/globalStorage/llvm-vs-code-extensions.vscode-clangd/install/19.1.2/clangd_19.1.2/lib/clang/19 -- /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"name":"Log","tid":250490,"ts":592161418.98399997},{"pid":0,"ph":"X","args":{},"dur":42.851999998092651,"name":"AdjustCompileFlags","tid":250166,"ts":592161408.14300001},{"pid":0,"ph":"i","args":{"Message":"ASTWorker building file /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp version 3 with command clangd fallback\n[/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils]\n/usr/lib/llvm-14/bin/clang -I/home/<USER>/Desktop/ysyx-workbench/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/nemu/src/isa/riscv32/include/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/build/obj_dir/ -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/klib/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/src/platform/nemu/include -I/home/<USER>/Desktop/ysyx-workbench/abstract-machine/am/include -I/home/<USER>/Desktop/ysyx-workbench/nvboard/include/ -I/usr/include/SDL2/ -I/usr/share/verilator/include/ -I/home/<USER>/Desktop/ysyx-workbench/npc/csrc/include -include=/headers/file.h -I/other/headers -resource-dir=/home/<USER>/.config/Code/User/globalStorage/llvm-vs-code-extensions.vscode-clangd/install/19.1.2/clangd_19.1.2/lib/clang/19 -- /home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"name":"Log","tid":250166,"ts":592161488.83700001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250167,"ts":592162608.72399998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":592162683.03600001},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":592162698.27499998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250491,"ts":592162766.32500005},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250167,"ts":592163116.43299997},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/utils/log.cpp"},"dur":6.593000054359436,"name":"getConfig","tid":250166,"ts":592163160.49699998},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250491,"ts":592163179.97500002},{"pid":0,"ph":"X","args":{},"dur":30.447999954223633,"name":"Build AST","tid":250166,"ts":592163158.21300006},{"pid":0,"ph":"X","args":{},"dur":1361.5889999866486,"name":"Update","tid":250166,"ts":592161232.32700002},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250166,"ts":592163211.43499994},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":7.6349999904632568,"name":"getConfig","tid":250490,"ts":592163219.08899999},{"pid":0,"ph":"X","args":{},"dur":36.218999981880188,"name":"Build AST","tid":250490,"ts":592163216.43400002},{"pid":0,"ph":"X","args":{},"dur":1474.9349999427795,"name":"Update","tid":250490,"ts":592161196.21800005},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}}},"dur":188.87099993228912,"name":"textDocument/didSave","tid":241390,"ts":592110958.48300004},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":592163286.38800001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/semanticTokens/full(116)"},"name":"Log","tid":241390,"ts":592413569.745},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":3.1059999465942383,"name":"Queued:SemanticHighlights","tid":241390,"ts":592413657.74300003},{"pid":0,"ph":"s","cat":"mock_cat","id":136,"name":"Context crosses threads","tid":241390,"ts":592413557.38100004},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":136,"name":"Context crosses threads","tid":250490,"ts":592413692.08800006},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":10.589999914169312,"name":"getConfig","tid":250490,"ts":592413696.54700005},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/semanticTokens/full(116) 0 ms"},"name":"Log","tid":250490,"ts":592413960.13999999},{"pid":0,"ph":"X","args":{},"dur":330.53100001811981,"name":"SemanticHighlights","tid":250490,"ts":592413685.48599994},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":{"data":[8,5,8,3,131073,1,5,9,3,131073,1,5,8,3,131073,0,21,8,2,16403,1,5,8,3,131073,1,5,13,3,131073,0,20,11,2,16387,0,18,8,2,16387,0,14,4,2,16387,4,5,7,3,131075,5,2,3,19,131072,1,2,6,3,131584,2,2,6,19,131072,5,5,18,3,131073,2,13,8,0,65539,0,11,4,19,131072,1,13,12,0,65539,0,15,4,19,131072,1,13,8,0,65539,0,11,4,19,131072,1,0,4,8,66048,0,6,8,0,131075,0,11,4,19,131072,1,11,13,0,65539,3,12,11,3,131073,1,11,10,3,65539,0,15,4,2,16387,0,12,4,2,16387,1,17,6,8,131584,0,7,5,1,16403,1,22,11,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,22,17,19,131072,0,19,4,19,131072,1,41,4,19,131072,2,8,1,1,16387,1,13,1,1,16384,0,2,1,21,0,0,2,11,3,131584,0,12,4,2,16384,0,6,4,2,16384,0,21,5,1,16400,0,7,4,19,131072,0,7,2,21,0,0,3,1,21,0,1,14,1,1,16384,1,18,18,3,131072,1,18,8,0,65536,0,9,1,21,0,0,2,6,0,131584,1,18,6,3,131584,0,7,6,0,131584,0,14,1,21,0,0,1,13,0,65536,1,18,12,0,65536,0,13,1,21,0,0,2,6,0,131584,1,18,8,0,131072,0,9,1,21,0,0,2,5,3,131584,0,6,6,0,131584,0,13,11,3,131072,1,16,8,0,65536,0,9,1,21,0,0,2,6,0,131584,2,10,6,3,131584,0,49,4,2,16384,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,6,3,131584,1,10,4,3,131584,10,12,8,3,65539,1,8,8,0,65536,0,9,2,21,0,0,3,4,19,131072,1,6,3,19,131072,3,4,4,8,66048,0,6,2,1,16387,0,5,5,3,131584,0,7,8,0,65536,1,4,6,19,131072,0,8,2,1,16384,0,25,8,0,65536,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,9,4,1,16387,0,7,5,3,131584,0,7,2,1,18432,2,4,3,19,131072,0,36,8,0,65536,0,10,4,1,16384,2,4,5,3,131584,0,7,2,1,18432,0,7,8,19,131072,1,8,3,1,16387,0,6,5,3,131584,0,7,13,3,131072,0,15,12,19,131072,0,15,4,1,16384,0,9,2,1,18432,1,4,6,19,131072,0,8,3,1,16384,0,4,2,21,0,2,4,6,3,131584,0,8,2,1,18432,1,11,4,1,16384,3,13,8,18,66048,0,9,3,0,65555,7,12,7,3,65539,2,11,12,19,131072,9,5,8,3,131075,2,2,6,3,131584,0,7,13,3,131072,0,14,12,19,131072,0,15,3,0,65552,0,12,3,0,65552,3,2,7,3,65536,3,5,12,3,131075,0,18,4,2,16387,0,12,4,2,16387,3,4,10,3,65536,0,12,4,2,16384,0,6,4,2,18432,2,4,9,3,131072,3,4,8,3,131072,0,10,8,0,65536,3,4,8,3,131072,2,4,8,3,131072,2,9,8,1,16387,0,11,8,3,65536,7,4,7,3,131072],"resultId":"7"}},"dur":108.98799991607666,"name":"textDocument/semanticTokens/full","tid":241390,"ts":592413557.38100004},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":592414051.16400003},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/inlayHint(117)"},"name":"Log","tid":241390,"ts":592596496.16299999},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.093999981880188,"name":"Queued:InlayHints","tid":241390,"ts":592596535.26800001},{"pid":0,"ph":"s","cat":"mock_cat","id":137,"name":"Context crosses threads","tid":241390,"ts":592596474.01100004},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":137,"name":"Context crosses threads","tid":250490,"ts":592596573.79100001},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":11.722999930381775,"name":"getConfig","tid":250490,"ts":592596609.27900004},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/inlayHint(117) 0 ms"},"name":"Log","tid":250490,"ts":592597340.505},{"pid":0,"ph":"X","args":{},"dur":1134.9759999513626,"name":"InlayHints","tid":250490,"ts":592596566.20700002},{"pid":0,"ph":"X","args":{"Params":{"range":{"end":{"character":3,"line":114},"start":{"character":0,"line":0}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":9,"line":22}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":41}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":41}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":41}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":41}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":41}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":42}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":42}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":42}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":42}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":42}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":43}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":43}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":43}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":43}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":43}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":44}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":44}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":44}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":44}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":44}},{"label":[{"value":"[4]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":45}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":45}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":45}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":45}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":47,"line":45}},{"label":[{"value":"[5]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":8,"line":46}},{"label":[{"value":".name="}],"paddingLeft":false,"paddingRight":false,"position":{"character":9,"line":46}},{"label":[{"value":".has_arg="}],"paddingLeft":false,"paddingRight":false,"position":{"character":22,"line":46}},{"label":[{"value":".flag="}],"paddingLeft":false,"paddingRight":false,"position":{"character":41,"line":46}},{"label":[{"value":".val="}],"paddingLeft":false,"paddingRight":false,"position":{"character":48,"line":46}},{"kind":2,"label":[{"value":"shortopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":41,"line":49}},{"kind":2,"label":[{"value":"longopts:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":56,"line":49}},{"kind":2,"label":[{"value":"longind:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":63,"line":49}},{"kind":2,"label":[{"value":"s:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":25,"line":53}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":33,"line":53}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":35,"line":55}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":42,"line":55}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":58}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":59}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":60}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":61}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":62}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":63}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":64}},{"kind":2,"label":[{"value":"format:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":17,"line":65}},{"kind":2,"label":[{"value":"status:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":66}},{"kind":2,"label":[{"value":"filename:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":22,"line":81}},{"kind":2,"label":[{"value":"modes:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":32,"line":81}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":84}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":84}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":84}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":23,"line":85}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":11,"line":89}},{"kind":2,"label":[{"value":"off:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":15,"line":89}},{"kind":2,"label":[{"value":"whence:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":18,"line":89}},{"kind":2,"label":[{"value":"ptr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":21,"line":90}},{"kind":2,"label":[{"value":"paddr:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":36,"line":90}},{"kind":2,"label":[{"value":"n:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":57,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":60,"line":90}},{"kind":2,"label":[{"value":"stream:"}],"paddingLeft":false,"paddingRight":true,"position":{"character":12,"line":93}},{"label":[{"value":"[0]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":98}},{"label":[{"value":"[1]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":99}},{"label":[{"value":"[2]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":100}},{"label":[{"value":"[3]="}],"paddingLeft":false,"paddingRight":false,"position":{"character":2,"line":101}}]},"dur":67.658999919891357,"name":"textDocument/inlayHint","tid":241390,"ts":592596474.01100004},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":592597857.171},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(118)"},"name":"Log","tid":241390,"ts":592613629.40400004},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":254542},{"pid":0,"ph":"X","args":{},"dur":13.376000046730042,"name":"WaitForFreeSemaphoreSlot","tid":254542,"ts":592613721.55999994},{"pid":0,"ph":"s","cat":"mock_cat","id":138,"name":"Context crosses threads","tid":241390,"ts":592613617.50199997},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":138,"name":"Context crosses threads","tid":254542,"ts":592613749.96399999},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":19.146000027656555,"name":"getConfig","tid":254542,"ts":592613744.42400002},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(118) 0 ms"},"name":"Log","tid":254542,"ts":592614144.67799997},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":72,"endLine":20,"kind":"comment","startCharacter":4,"startLine":17},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":82.736999988555908,"name":"textDocument/foldingRange","tid":241390,"ts":592613617.50199997},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(119)"},"name":"Log","tid":241390,"ts":592663164.23800004},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.7749999761581421,"name":"Queued:codeAction","tid":241390,"ts":592663213.75199997},{"pid":0,"ph":"s","cat":"mock_cat","id":139,"name":"Context crosses threads","tid":241390,"ts":592663139.54999995},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":139,"name":"Context crosses threads","tid":250490,"ts":592663253.47800004},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":19.095999956130981,"name":"getConfig","tid":250490,"ts":592663258.19700003},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(119) 0 ms"},"name":"Log","tid":250490,"ts":592663330.87600005},{"pid":0,"ph":"X","args":{},"dur":119.93899989128113,"name":"codeAction","tid":250490,"ts":592663246.81500006},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":12,"line":24},"start":{"character":12,"line":24}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":85.013000011444092,"name":"textDocument/codeAction","tid":241390,"ts":592663139.54999995},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":592663393.91600001},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/foldingRange(120)"},"name":"Log","tid":241390,"ts":592763705.403},{"pid":0,"ph":"M","args":{"name":"FoldingRanges"},"name":"thread_name","tid":254543},{"pid":0,"ph":"X","args":{},"dur":11.982999920845032,"name":"WaitForFreeSemaphoreSlot","tid":254543,"ts":592763823.24800003},{"pid":0,"ph":"s","cat":"mock_cat","id":140,"name":"Context crosses threads","tid":241390,"ts":592763687.66900003},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":140,"name":"Context crosses threads","tid":254543,"ts":592763851.39199996},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":26.671000003814697,"name":"getConfig","tid":254543,"ts":592763845.13},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/foldingRange(120) 0 ms"},"name":"Log","tid":254543,"ts":592764221.70899999},{"pid":0,"ph":"X","args":{"Params":{"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[{"endLine":24,"kind":"region","startCharacter":17,"startLine":16},{"endLine":70,"kind":"region","startCharacter":47,"startLine":39},{"endCharacter":4,"endLine":46,"kind":"region","startCharacter":35,"startLine":40},{"endCharacter":4,"endLine":67,"kind":"region","startCharacter":78,"startLine":49},{"endCharacter":6,"endLine":66,"kind":"region","startCharacter":18,"startLine":50},{"endLine":94,"kind":"region","startCharacter":29,"startLine":76},{"endCharacter":4,"endLine":79,"kind":"region","startCharacter":27,"startLine":77},{"endLine":101,"kind":"region","startCharacter":32,"startLine":97},{"endLine":109,"kind":"region","startCharacter":23,"startLine":104},{"endLine":120,"kind":"region","startCharacter":17,"startLine":115},{"endLine":145,"kind":"region","startCharacter":44,"startLine":123},{"endCharacter":72,"endLine":20,"kind":"comment","startCharacter":4,"startLine":17},{"endCharacter":1,"endLine":74,"kind":"comment","startCharacter":2,"startLine":73},{"endCharacter":1,"endLine":113,"kind":"comment","startCharacter":2,"startLine":112},{"endCharacter":58,"endLine":141,"kind":"comment","startCharacter":6,"startLine":140},{"endCharacter":67,"endLine":144,"kind":"comment","startCharacter":6,"startLine":143}]},"dur":115.50099992752075,"name":"textDocument/foldingRange","tid":241390,"ts":592763687.66900003},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(121)"},"name":"Log","tid":241390,"ts":593106288.95799994},{"pid":0,"ph":"X","args":{"PreambleRequestsNames":[],"RequestsNames":[]},"dur":2.0139999389648438,"name":"Queued:codeAction","tid":241390,"ts":593106338.18200004},{"pid":0,"ph":"s","cat":"mock_cat","id":141,"name":"Context crosses threads","tid":241390,"ts":593106263.45899999},{"pid":0,"ph":"f","bp":"e","cat":"mock_cat","id":141,"name":"Context crosses threads","tid":250490,"ts":593106375.13300002},{"pid":0,"ph":"X","args":{"path":"/home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"},"dur":12.784999966621399,"name":"getConfig","tid":250490,"ts":593106380.64300001},{"pid":0,"ph":"i","args":{"Message":"--> reply:textDocument/codeAction(121) 0 ms"},"name":"Log","tid":250490,"ts":593106458.52199996},{"pid":0,"ph":"X","args":{},"dur":128.23499989509583,"name":"codeAction","tid":250490,"ts":593106368.57000005},{"pid":0,"ph":"X","args":{"Params":{"context":{"diagnostics":[],"triggerKind":2},"range":{"end":{"character":72,"line":20},"start":{"character":72,"line":20}},"textDocument":{"uri":"file:///home/<USER>/Desktop/auto-test/ysyx-local-autotest/test-framework/ysyx-workbench/npc/csrc/monitor/monitor.cpp"}},"Reply":[]},"dur":81.526000022888184,"name":"textDocument/codeAction","tid":241390,"ts":593106263.45899999},{"pid":0,"ph":"i","args":{"Message":"--> textDocument/clangd.fileStatus"},"name":"Log","tid":250490,"ts":593106534.95799994},{"pid":0,"ph":"i","args":{"Message":"<-- textDocument/codeAction(122)"},"name":"Log","tid":2413