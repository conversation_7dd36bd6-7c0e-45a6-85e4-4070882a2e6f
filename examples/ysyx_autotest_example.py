#!/usr/bin/env python3
"""
Example usage of YSYXSoC Auto-Test integration in RTL2GDS workflow
"""

import os
import sys
import tempfile
from pathlib import Path

# Add RTL2GDS to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from rtl2gds.chip import Chip
from rtl2gds.flow.step_wrapper import StepWrapper
from rtl2gds.flow.single_step import run as run_single_step
from rtl2gds.step.step_template import Step


def create_example_rtl():
    """Create an example RTL file for testing"""
    rtl_content = '''
module ysyx_22050499 (
    input wire clk,
    input wire rst,
    input wire [31:0] data_in,
    output reg [31:0] data_out
);

// Simple counter design
reg [31:0] counter;

always @(posedge clk or posedge rst) begin
    if (rst) begin
        counter <= 32'h0;
        data_out <= 32'h0;
    end else begin
        counter <= counter + 1;
        data_out <= data_in + counter;
    end
end

endmodule
'''
    
    # Create temporary RTL file
    rtl_file = "example_ysyx_cpu.v"
    with open(rtl_file, 'w') as f:
        f.write(rtl_content)
    
    return rtl_file


def example_standalone_step():
    """Example: Run ysyx_autotest as a standalone step"""
    print("=" * 60)
    print("Example 1: Standalone ysyx_autotest step")
    print("=" * 60)
    
    # Create example RTL file
    rtl_file = create_example_rtl()
    print(f"Created example RTL file: {rtl_file}")
    
    try:
        # Create ysyx_autotest step
        step = Step("ysyx_autotest")
        
        # Configure parameters
        parameters = {
            "RTL_FILE": rtl_file,
            "TOP_NAME": "ysyx_22050499",
            "RESULT_DIR": "ysyx_test_results",
            "STAGE": "B",
            "ARCH": "riscv32e-ysyxsoc",
            "MAX_SIMULATE_TIME": "1000000000",
            "MAINARGS": "train",
            "TESTS": "cpu-tests",  # Run only CPU tests for faster execution
        }
        
        print("Running ysyx_autotest step with parameters:")
        for key, value in parameters.items():
            print(f"  {key}: {value}")
        
        # Run the step
        print("\nExecuting ysyx_autotest...")
        runtime_log, step_reproducible, subprocess_metrics = step.run(
            parameters=parameters,
            output_prefix="example"
        )
        
        print("✓ ysyx_autotest completed successfully!")
        print(f"Output files: {list(step_reproducible['output_files'].keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ ysyx_autotest failed: {e}")
        print("Note: This is expected if Docker is not available or configured")
        return False
    
    finally:
        # Clean up
        if os.path.exists(rtl_file):
            os.remove(rtl_file)


def example_chip_integration():
    """Example: Run ysyx_autotest using Chip and StepWrapper"""
    print("=" * 60)
    print("Example 2: ysyx_autotest with Chip integration")
    print("=" * 60)
    
    # Create example RTL file
    rtl_file = create_example_rtl()
    print(f"Created example RTL file: {rtl_file}")
    
    try:
        # Create chip configuration
        chip_config = {
            "top_name": "ysyx_22050499",
            "path_setting": {
                "rtl_file": rtl_file,
                "result_dir": "chip_ysyx_results"
            },
            "constrain": {
                "clk_port_name": "clk",
                "clk_freq_mhz": 100,
                "core_util": 0.5
            }
        }
        
        # Create chip from config
        chip = Chip.from_dict(chip_config)
        
        # Create step wrapper
        wrapper = StepWrapper(chip)
        
        print("Running ysyx_autotest using StepWrapper...")
        
        # Run ysyx_autotest
        result_files = wrapper.run_ysyx_autotest()
        
        print("✓ ysyx_autotest completed successfully!")
        print(f"Result files: {list(result_files.keys())}")
        print(f"Chip finished step: {chip.finished_step}")
        
        return True
        
    except Exception as e:
        print(f"✗ ysyx_autotest failed: {e}")
        print("Note: This is expected if Docker is not available or configured")
        return False
    
    finally:
        # Clean up
        if os.path.exists(rtl_file):
            os.remove(rtl_file)


def example_single_step_runner():
    """Example: Run ysyx_autotest using single step runner"""
    print("=" * 60)
    print("Example 3: ysyx_autotest with single step runner")
    print("=" * 60)
    
    # Create example RTL file
    rtl_file = create_example_rtl()
    print(f"Created example RTL file: {rtl_file}")
    
    try:
        # Create chip configuration
        chip_config = {
            "top_name": "ysyx_22050499",
            "path_setting": {
                "rtl_file": rtl_file,
                "result_dir": "single_step_results"
            },
            "constrain": {
                "clk_port_name": "clk",
                "clk_freq_mhz": 100,
                "core_util": 0.5
            }
        }
        
        # Create chip from config
        chip = Chip.from_dict(chip_config)
        
        print("Running ysyx_autotest using single step runner...")
        
        # Run ysyx_autotest step
        result_files = run_single_step(
            chip=chip,
            expect_step="ysyx_autotest"
        )
        
        print("✓ ysyx_autotest completed successfully!")
        print(f"Result files: {list(result_files.keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ ysyx_autotest failed: {e}")
        print("Note: This is expected if Docker is not available or configured")
        return False
    
    finally:
        # Clean up
        if os.path.exists(rtl_file):
            os.remove(rtl_file)


def example_custom_configuration():
    """Example: Custom ysyx_autotest configuration"""
    print("=" * 60)
    print("Example 4: Custom ysyx_autotest configuration")
    print("=" * 60)
    
    # Create example RTL file
    rtl_file = create_example_rtl()
    print(f"Created example RTL file: {rtl_file}")
    
    try:
        # Create step with custom configuration
        step = Step("ysyx_autotest")
        
        # Custom parameters for specific testing scenario
        custom_parameters = {
            "RTL_FILE": rtl_file,
            "TOP_NAME": "ysyx_22050499",
            "RESULT_DIR": "custom_test_results",
            "STAGE": "B",
            "ARCH": "riscv32e-ysyxsoc",
            "MAX_SIMULATE_TIME": "500000000",  # Shorter simulation time
            "MAINARGS": "test",  # Different microbench args
            "TESTS": "coremark",  # Run only CoreMark benchmark
        }
        
        print("Custom configuration:")
        for key, value in custom_parameters.items():
            print(f"  {key}: {value}")
        
        print("\nExecuting custom ysyx_autotest...")
        runtime_log, step_reproducible, subprocess_metrics = step.run(
            parameters=custom_parameters,
            output_prefix="custom"
        )
        
        print("✓ Custom ysyx_autotest completed successfully!")
        
        # Display metrics if available
        if subprocess_metrics:
            print("Metrics:")
            for key, value in subprocess_metrics.items():
                print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"✗ Custom ysyx_autotest failed: {e}")
        print("Note: This is expected if Docker is not available or configured")
        return False
    
    finally:
        # Clean up
        if os.path.exists(rtl_file):
            os.remove(rtl_file)


def main():
    """Run all examples"""
    print("YSYXSoC Auto-Test Integration Examples")
    print("=" * 60)
    print("These examples demonstrate different ways to use the ysyx_autotest")
    print("step in RTL2GDS workflows. Note that actual execution requires")
    print("Docker to be installed and configured.")
    print()
    
    examples = [
        ("Standalone Step", example_standalone_step),
        ("Chip Integration", example_chip_integration),
        ("Single Step Runner", example_single_step_runner),
        ("Custom Configuration", example_custom_configuration),
    ]
    
    results = []
    for name, example_func in examples:
        try:
            success = example_func()
            results.append((name, success))
        except Exception as e:
            print(f"Example '{name}' failed with exception: {e}")
            results.append((name, False))
        print()
    
    # Summary
    print("=" * 60)
    print("Example Results Summary:")
    print("=" * 60)
    for name, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"{name}: {status}")
    
    print("\nNote: Failures are expected if Docker is not available.")
    print("The examples demonstrate the integration API and configuration.")


if __name__ == "__main__":
    main()
