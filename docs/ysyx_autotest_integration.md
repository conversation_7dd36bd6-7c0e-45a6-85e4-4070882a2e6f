# YSYXSoC Auto-Test Integration

This document describes the integration of the YSYXSoC Auto-Test tool into the RTL2GDS workflow.

## Overview

The YSYXSoC Auto-Test tool has been integrated as an optional step in the RTL2GDS pipeline, allowing users to automatically test their RTL designs using the YSYX SoC test framework. This integration provides comprehensive testing including CPU tests, benchmarks (CoreMark, Dhrystone, MicroBench), and performance analysis.

## Features

- **CPU Tests**: Validates basic CPU functionality with RISC-V instruction tests
- **Benchmark Tests**: Performance evaluation using industry-standard benchmarks
- **Configurable Parameters**: Flexible configuration for different test scenarios
- **Docker-based Execution**: Isolated test environment with all dependencies
- **Comprehensive Reporting**: JSON-based results with detailed metrics

## Step Configuration

The `ysyx_autotest` step is defined in `src/rtl2gds/step/step.yaml` with the following configuration:

### Input Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `TOP_NAME` | string | `ysyx_22050499` | Top module name for the design |
| `STAGE` | string | `B` | YSYX stage (B or D) |
| `ARCH` | string | `riscv32e-ysyxsoc` | Target architecture |
| `MAX_SIMULATE_TIME` | integer | `1000000000` | Maximum simulation time |
| `MAINARGS` | string | `train` | Arguments for microbench |
| `TESTS` | string | `all` | Tests to run (all, cpu-tests, coremark, dhrystone, microbench) |

### Input Files

| File | Required | Description |
|------|----------|-------------|
| `RTL_FILE` | Optional | Single RTL file path |
| `RTL_DIR` | Optional | Directory containing RTL files |

### Output Files

| File | Description |
|------|-------------|
| `CPU_TEST_RESULT_JSON` | CPU test results in JSON format |
| `BENCHMARK_RESULT_JSON` | Benchmark results in JSON format |
| `CPU_TEST_LOG` | CPU test execution log |
| `COREMARK_LOG` | CoreMark benchmark log |
| `DHRYSTONE_LOG` | Dhrystone benchmark log |
| `MICROBENCH_LOG` | MicroBench benchmark log |
| `YSYX_AUTOTEST_SUMMARY_JSON` | Comprehensive test summary |

### Output Metrics

- `cpu_test_status`: Overall CPU test status (pass/fail)
- `cpu_test_pass_count`: Number of passed CPU tests
- `cpu_test_total_count`: Total number of CPU tests
- `coremark_status`: CoreMark benchmark status
- `coremark_pass_count`: Number of passed CoreMark tests
- `coremark_total_count`: Total number of CoreMark tests
- `dhrystone_status`: Dhrystone benchmark status
- `microbench_status`: MicroBench status
- `microbench_pass_count`: Number of passed MicroBench tests
- `microbench_total_count`: Total number of MicroBench tests
- `total_simulation_time_ms`: Total simulation time in milliseconds

## Usage Examples

### Standalone Execution

```python
from rtl2gds.chip import Chip
from rtl2gds.flow.single_step import run

# Create chip configuration
chip = Chip(config_yaml="path/to/config.yaml")

# Run ysyx_autotest step
result_files = run(
    chip=chip,
    expect_step="ysyx_autotest"
)
```

### Using StepWrapper

```python
from rtl2gds.chip import Chip
from rtl2gds.flow.step_wrapper import StepWrapper

# Create chip and wrapper
chip = Chip(config_yaml="path/to/config.yaml")
wrapper = StepWrapper(chip)

# Run ysyx_autotest
result_files = wrapper.run_ysyx_autotest()
```

### Configuration Example

```yaml
# chip_config.yaml
top_name: "ysyx_22050499"
path_setting:
  rtl_file: "path/to/design.v"
  result_dir: "results"
constrain:
  clk_port_name: "clk"
  clk_freq_mhz: 100
```

### Custom Test Configuration

```python
# Run only CPU tests with custom parameters
from rtl2gds.step.step_template import Step

step = Step("ysyx_autotest")
result = step.run(
    parameters={
        "RTL_FILE": "design.v",
        "TOP_NAME": "my_cpu",
        "STAGE": "B",
        "TESTS": "cpu-tests",
        "MAX_SIMULATE_TIME": "500000000",
        "RESULT_DIR": "test_results"
    },
    output_prefix="01"
)
```

## Docker Requirements

The ysyx_autotest step requires Docker to be installed and accessible. The integration automatically:

1. Builds the ysyx_autotest Docker image from `tools/ysyx_autotest/Dockerfile`
2. Mounts RTL files and result directories as volumes
3. Executes the test framework inside the container
4. Collects results and logs

### Docker Image Contents

- Ubuntu 22.04 base image
- Verilator for RTL simulation
- RISC-V toolchain for cross-compilation
- YSYXSoC test framework
- Python environment with required dependencies

## Integration Architecture

The ysyx_autotest integration follows the RTL2GDS step architecture:

1. **Step Definition**: Configured in `step.yaml` with tool environment and parameters
2. **Execution Logic**: Implemented in `src/rtl2gds/step/ysyx_autotest.py`
3. **Wrapper Integration**: Added to `StepWrapper` class for pipeline integration
4. **Router Support**: Included in single-step execution router

### File Structure

```
src/rtl2gds/
├── step/
│   ├── ysyx_autotest.py          # Main execution logic
│   └── step.yaml                 # Step configuration
├── flow/
│   ├── step_wrapper.py           # Wrapper integration
│   └── single_step.py            # Router support
└── global_configs.py             # Step name definition

tools/ysyx_autotest/              # YSYXSoC Auto-Test tool
├── Dockerfile                    # Docker environment
├── scripts/                      # Test execution scripts
└── ysyx-local-autotest/         # Test framework
```

## Error Handling

The integration includes comprehensive error handling:

- **Docker Availability**: Checks for Docker installation and accessibility
- **RTL File Validation**: Verifies RTL files exist before testing
- **Timeout Protection**: Prevents infinite execution with configurable timeouts
- **Result Validation**: Ensures test outputs are properly generated
- **Graceful Degradation**: Provides meaningful error messages for common issues

## Performance Considerations

- **Docker Build Caching**: Docker image is built once and reused
- **Selective Testing**: Option to run specific test suites for faster execution
- **Resource Management**: Configurable simulation time limits
- **Parallel Execution**: Multiple test instances can run simultaneously

## Troubleshooting

### Common Issues

1. **Docker not found**: Ensure Docker is installed and in PATH
2. **Permission denied**: Check Docker daemon permissions
3. **RTL file not found**: Verify RTL file paths are correct
4. **Simulation timeout**: Increase `MAX_SIMULATE_TIME` parameter
5. **Test failures**: Check RTL design compatibility with YSYX SoC

### Debug Mode

Enable debug logging for detailed execution information:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Future Enhancements

- Support for custom test configurations
- Integration with CI/CD pipelines
- Performance regression testing
- Multi-architecture support
- Cloud-based execution options
