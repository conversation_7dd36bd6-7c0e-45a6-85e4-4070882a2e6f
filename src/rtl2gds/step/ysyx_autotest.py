"""
YSYXSoC Auto-Test step implementation for RTL2GDS workflow
"""

import json
import logging
import os
import shutil
import subprocess
import tempfile
from pathlib import Path
from typing import Any, Dict

from rtl2gds.global_configs import StepName


def run(
    top_name: str,
    result_dir: str,
    rtl_file: str = None,
    rtl_dir: str = None,
    stage: str = "B",
    arch: str = "riscv32e-ysyxsoc",
    max_simulate_time: int = 1000000000,
    mainargs: str = "train",
    tests: str = "all",
) -> Dict[str, Any]:
    """
    Run YSYXSoC Auto-Test step.

    Args:
        top_name (str): Top module name for the design
        result_dir (str): Directory to store results
        rtl_file (str, optional): Single RTL file path
        rtl_dir (str, optional): Directory containing RTL files
        stage (str): YSYX stage (B or D), default "B"
        arch (str): Target architecture, default "riscv32e-ysyxsoc"
        max_simulate_time (int): Maximum simulation time, default 1000000000
        mainargs (str): Arguments for microbench, default "train"
        tests (str): Tests to run (all, cpu-tests, coremark, dhrystone, microbench), default "all"

    Returns:
        dict: Test results and metrics
    """
    logging.info("Starting YSYXSoC Auto-Test step")

    # Ensure result directory exists
    os.makedirs(result_dir, exist_ok=True)

    # Prepare RTL files for testing
    rtl_staging_dir = _prepare_rtl_files(rtl_file, rtl_dir, result_dir)

    # Create ysyx_autotest configuration
    config_data = _create_ysyx_config(
        top_name=top_name,
        rtl_staging_dir=rtl_staging_dir,
        result_dir=result_dir,
        stage=stage,
        arch=arch,
        max_simulate_time=max_simulate_time,
    )

    # Run ysyx_autotest in Docker container
    test_results = _run_ysyx_autotest_docker(
        config_data=config_data, result_dir=result_dir, mainargs=mainargs, tests=tests
    )

    # Process and collect results
    metrics = _collect_test_metrics(test_results, result_dir)

    # Generate summary report
    _generate_summary_report(metrics, result_dir, top_name)

    logging.info("YSYXSoC Auto-Test step completed")
    return metrics


def _prepare_rtl_files(rtl_file: str, rtl_dir: str, result_dir: str) -> str:
    """Prepare RTL files for ysyx_autotest"""
    rtl_staging_dir = os.path.join(result_dir, "rtl_staging")
    os.makedirs(rtl_staging_dir, exist_ok=True)

    if rtl_file and os.path.exists(rtl_file):
        # Copy single RTL file
        shutil.copy2(rtl_file, rtl_staging_dir)
        logging.info(f"Copied RTL file: {rtl_file}")
    elif rtl_dir and os.path.exists(rtl_dir):
        # Copy all RTL files from directory
        for ext in [".v", ".sv", ".svh", ".vh"]:
            for file_path in Path(rtl_dir).rglob(f"*{ext}"):
                shutil.copy2(file_path, rtl_staging_dir)
                logging.info(f"Copied RTL file: {file_path}")
    else:
        logging.warning("No RTL files specified or found")

    return rtl_staging_dir


def _create_ysyx_config(
    top_name: str,
    rtl_staging_dir: str,
    result_dir: str,
    stage: str,
    arch: str,
    max_simulate_time: int,
) -> Dict[str, Any]:
    """Create ysyx_autotest configuration"""
    config_data = {
        "YSYX_HOME": "/ysyx-local-autotest/test-framework/ysyx-workbench",
        "NEMU_HOME": "/ysyx-local-autotest/test-framework/ysyx-workbench/nemu",
        "AM_HOME": "/ysyx-local-autotest/test-framework/ysyx-workbench/abstract-machine",
        "AM_KERNELS_HOME": "/ysyx-local-autotest/test-framework/ysyx-workbench/am-kernels",
        "NPC_HOME": "/ysyx-local-autotest/test-framework/ysyx-workbench/npc",
        "SOC_HOME": "/ysyx-local-autotest/test-framework/ysyx-workbench/ysyxSoC",
        "HOST_RTL_DIR": "/mnt/rtl",
        "HOST_LOG_DIR": "/mnt/log",
        "TOP_NAME": top_name,
        "MAX_SIMULATE_TIME": max_simulate_time,
        "STAGE": stage,
        "ARCH": arch,
    }

    # Save config to file
    config_file = os.path.join(result_dir, "ysyx_config.yaml")
    try:
        import yaml

        with open(config_file, "w") as f:
            yaml.dump(config_data, f, default_flow_style=False)
    except ImportError:
        # Fallback to manual YAML writing if PyYAML is not available
        with open(config_file, "w") as f:
            for key, value in config_data.items():
                f.write(f"{key}: {value}\n")

    return config_data


def _run_ysyx_autotest_docker(
    config_data: Dict[str, Any], result_dir: str, mainargs: str, tests: str
) -> Dict[str, Any]:
    """Run ysyx_autotest in Docker container"""

    # Prepare Docker volumes
    rtl_staging_dir = os.path.join(result_dir, "rtl_staging")
    log_dir = os.path.join(result_dir, "logs")
    os.makedirs(log_dir, exist_ok=True)

    # Build Docker image if it doesn't exist
    ysyx_autotest_dir = os.path.join(os.path.dirname(__file__), "../../../tools/ysyx_autotest")
    docker_build_cmd = [
        "docker",
        "build",
        "-t",
        "ysyx-local-autotest",
        "-f",
        os.path.join(ysyx_autotest_dir, "Dockerfile"),
        ysyx_autotest_dir,
    ]

    logging.info("Building ysyx_autotest Docker image...")
    subprocess.run(docker_build_cmd, check=True, capture_output=True)

    # Run Docker container
    docker_run_cmd = [
        "docker",
        "run",
        "--rm",
        "-v",
        f"{rtl_staging_dir}:/mnt/rtl",
        "-v",
        f"{log_dir}:/mnt/log",
        "ysyx-local-autotest",
        "python3",
        "-m",
        "scripts",
    ]

    # Set environment variables for the container
    env = os.environ.copy()
    env.update({"MAINARGS": mainargs, "TESTS": tests})

    logging.info("Running ysyx_autotest in Docker container...")
    try:
        result = subprocess.run(
            docker_run_cmd, env=env, capture_output=True, text=True, timeout=3600  # 1 hour timeout
        )

        if result.returncode != 0:
            logging.error(f"ysyx_autotest failed with return code {result.returncode}")
            logging.error(f"stderr: {result.stderr}")

        logging.info(f"ysyx_autotest stdout: {result.stdout}")

    except subprocess.TimeoutExpired:
        logging.error("ysyx_autotest timed out after 1 hour")
        raise

    return {"returncode": result.returncode, "stdout": result.stdout, "stderr": result.stderr}


def _collect_test_metrics(test_results: Dict[str, Any], result_dir: str) -> Dict[str, Any]:
    """Collect and parse test metrics from ysyx_autotest results"""
    log_dir = os.path.join(result_dir, "logs")

    metrics = {
        "cpu_test_status": "fail",
        "cpu_test_pass_count": 0,
        "cpu_test_total_count": 0,
        "coremark_status": "fail",
        "coremark_pass_count": 0,
        "coremark_total_count": 0,
        "dhrystone_status": "fail",
        "microbench_status": "fail",
        "microbench_pass_count": 0,
        "microbench_total_count": 0,
        "total_simulation_time_ms": 0,
    }

    # Parse CPU test results
    cpu_test_json = os.path.join(log_dir, "cpu_test_result.json")
    if os.path.exists(cpu_test_json):
        try:
            with open(cpu_test_json, "r") as f:
                cpu_data = json.load(f)
            metrics["cpu_test_status"] = cpu_data.get("cpu-test", "fail")
            if "cpu-test_stat" in cpu_data:
                parts = cpu_data["cpu-test_stat"].split("/")
                if len(parts) == 2:
                    metrics["cpu_test_pass_count"] = int(parts[0])
                    metrics["cpu_test_total_count"] = int(parts[1])
        except Exception as e:
            logging.warning(f"Failed to parse CPU test results: {e}")

    # Parse benchmark results
    benchmark_json = os.path.join(log_dir, "benchmark_result.json")
    if os.path.exists(benchmark_json):
        try:
            with open(benchmark_json, "r") as f:
                bench_data = json.load(f)

            metrics["coremark_status"] = bench_data.get("coremark", "fail")
            if "coremark_stat" in bench_data:
                parts = bench_data["coremark_stat"].split("/")
                if len(parts) == 2:
                    metrics["coremark_pass_count"] = int(parts[0])
                    metrics["coremark_total_count"] = int(parts[1])

            metrics["dhrystone_status"] = bench_data.get("dhrystone", "fail")
            metrics["microbench_status"] = bench_data.get("microbench", "fail")

            if "microbench_stat" in bench_data:
                parts = bench_data["microbench_stat"].split("/")
                if len(parts) == 2:
                    metrics["microbench_pass_count"] = int(parts[0])
                    metrics["microbench_total_count"] = int(parts[1])

        except Exception as e:
            logging.warning(f"Failed to parse benchmark results: {e}")

    return metrics


def _generate_summary_report(metrics: Dict[str, Any], result_dir: str, top_name: str):
    """Generate a summary report of the ysyx_autotest results"""
    summary = {
        "step_name": "ysyx_autotest",
        "top_name": top_name,
        "timestamp": str(Path().cwd()),  # placeholder
        "overall_status": "pass" if _all_tests_passed(metrics) else "fail",
        "test_results": {
            "cpu_test": {
                "status": metrics["cpu_test_status"],
                "passed": metrics["cpu_test_pass_count"],
                "total": metrics["cpu_test_total_count"],
            },
            "coremark": {
                "status": metrics["coremark_status"],
                "passed": metrics["coremark_pass_count"],
                "total": metrics["coremark_total_count"],
            },
            "dhrystone": {"status": metrics["dhrystone_status"]},
            "microbench": {
                "status": metrics["microbench_status"],
                "passed": metrics["microbench_pass_count"],
                "total": metrics["microbench_total_count"],
            },
        },
        "metrics": metrics,
    }

    summary_file = os.path.join(result_dir, f"{top_name}_ysyx_autotest_summary.json")
    with open(summary_file, "w") as f:
        json.dump(summary, f, indent=2)

    logging.info(f"Generated ysyx_autotest summary: {summary_file}")


def _all_tests_passed(metrics: Dict[str, Any]) -> bool:
    """Check if all tests passed"""
    return (
        metrics["cpu_test_status"] == "pass"
        and metrics["coremark_status"] == "pass"
        and metrics["dhrystone_status"] == "pass"
        and metrics["microbench_status"] == "pass"
    )
